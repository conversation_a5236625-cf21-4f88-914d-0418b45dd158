<template>
  <el-dialog
      v-model="isShow"
      :title="title"
      width="400"
      :before-close="onClose"
      class="xy-offset-dialog"
  >
    <el-form :model="formData" label-position="top" :rules="rules" ref="displacementForm">
      <!-- 新版：遍历 ids，每个 id 代表一页，添加全选复选框 -->
      <el-form-item label="选择需要修改的页面" v-if="showPageSelector">
        <!-- 全选复选框 -->
        <el-checkbox v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <!-- 遍历每一页 -->
        <el-checkbox-group v-model="selectedIds" style="display: block; margin-left: 20px;">
          <el-checkbox
              v-for="(page, index) in ids"
              :key="page"
              :label="page">
            页{{ (index+1) }}<span v-if="page === id"> (当前预览页)</span>
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 位移参数输入 - 只在showOffset为true时显示 -->
      <el-form-item label="X轴位移大小" prop="xOffset" v-if="showOffset">
        <el-input-number v-model="formData.xOffset" :step="20" style="width: 100%;" />
      </el-form-item>
      <el-form-item label="Y轴位移大小" prop="yOffset" v-if="showOffset">
        <el-input-number v-model="formData.yOffset" :step="20" style="width: 100%;" />
      </el-form-item>

      <!-- 勾叉大小设置 -->
      <el-form-item label="勾叉大小 默认80" v-if="setMarkZoom">
        <el-input-number v-model="markZoom" :step="10" :value="80" style="width: 100%;" />
      </el-form-item>

      <!-- 字体大小设置 -->
      <el-form-item label="字体大小 默认80">
        <el-input-number v-model="fontSize" :step="10" :value="80" style="width: 100%;" />
      </el-form-item>

      <!-- 透明度设置 -->
      <el-form-item label="勾叉字体透明度 默认0.6">
        <el-input-number 
          v-model="opacity" 
          :min="0"
          :max="1"
          :step="0.1"
          :precision="1"
          :value="0.6"
          style="width: 100%;" 
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onCancelSave">取消保存</el-button>
      <el-button type="primary" @click="onSubmit" :loading="isSaving">确认保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { mapActions } from "pinia";
import { useUserStore } from "@/store";
const store = useUserStore();

export default {
  name: "DisplacementDialog",
  props: {
    submitting: {
      type: Boolean,
      default: false,
    },
    id: {  // 当前预览页的 id
      type: String,
      default: ''
    },
    ids: { // 所有页面的 id 数组
      type: Array,
      default: () => []
    },
    setMarkZoom: {
      type: Boolean,
      default: false,
    },
    showPageSelector: {
      type: Boolean,
      default: true,
    },
    showOffset: {
      type: Boolean,
      default: true,
    },
    useUpdateInterface: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      isSaving: false,
      isShow: false,
      // 用于多选选中页面的 ids
      selectedIds: [],
      // 全选状态（复选框的 v-model 为布尔值）
      checkAll: false,
      formData: {
        xOffset: 0,
        yOffset: 0,
      },
      markZoom: 80,
      fontSize: 80,
      opacity: 0.6,
      rules: {
        xOffset: [
          { required: true, message: "请输入X轴位移大小", trigger: "blur" },
          { type: "number", message: "X轴位移大小必须是数字", trigger: "blur" },
        ],
        yOffset: [
          { required: true, message: "请输入Y轴位移大小", trigger: "blur" },
          { type: "number", message: "Y轴位移大小必须是数字", trigger: "blur" },
        ]
      },
      // 保存初始位移参数，用于取消保存时恢复（当前页）
      initFormData: {
        xOffset: 0,
        yOffset: 0,
      },
      // 存储每个页面初始的位移数据（按 ids 顺序保存）
      idsInitFormData: []
    };
  },
  computed: {
    // 根据showOffset动态设置标题
    title() {
      return this.showOffset ? "设置位移、大小、透明度参数" : "设置大小、透明度参数";
    }
  },
  watch: {
    // 当位移数据改变时，实时更新所有已选择页面的预览数据
    formData: {
      handler(newVal) {
        this.selectedIds.forEach(pageId => {
          this.setXYOffsetData(pageId, { ...this.formData });
        });
      },
      deep: true
    },
    markZoom: {
      handler(newVal) {
        this.selectedIds.forEach(pageId => {
          store.setMarkZoomDataById(pageId, newVal);
        });
      },
      deep: true
    },
    fontSize: {
      handler(newVal) {
        this.selectedIds.forEach(pageId => {
          store.setFontSizeDataById(pageId, newVal);
        });
      },
      deep: true
    },
    opacity: {
      handler(newVal) {
        this.selectedIds.forEach(pageId => {
          store.setOpacityDataById(pageId, newVal);
        });
      },
      deep: true
    },
    // 监控选中页面数组，更新全选复选框状态
    selectedIds(newVal) {
      this.checkAll = newVal.length === this.ids.length;
    }
  },
  methods: {
    ...mapActions(useUserStore, ['setXYOffsetData']),

    // 格式化透明度显示
    formatOpacity(val) {
      return val.toFixed(1);
    },

    // 显示对话框时：同步当前页数据，默认选中所有页面
    show() {
      // 获取当前预览页的参数
      this.formData = { ...store.getXYOffsetData(this.id) };
      
      // 根据showPageSelector决定页面选择逻辑
      if (this.showPageSelector) {
        // 默认选中所有页面
        this.selectedIds = [this.id];
        this.checkAll = true;
        // 保存初始数据：当前页与所有页面
        this.initFormData = { ...this.formData };
        this.idsInitFormData = [];
        this.ids.forEach(pageId => {
          this.idsInitFormData.push({ ...store.getXYOffsetData(pageId) });
        });
      } else {
        // 不显示页面选择器时，只选择当前页面
        this.selectedIds = [this.id];
        this.checkAll = false;
        // 只保存当前页面的初始数据
        this.initFormData = { ...this.formData };
        this.idsInitFormData = [{ ...this.formData }];
      }
      
      if (this.setMarkZoom) {
        this.markZoom = store.getMarkZoomDataById(this.id);
      }
      this.fontSize = store.getFontSizeDataById(this.id);
      this.opacity = store.getOpacityDataById(this.id);
      this.isShow = true;
    },
    // 关闭对话框
    onClose() {
      this.isShow = false;
      this.$emit('close');
    },
    // 全选复选框改变处理方法
    handleCheckAllChange(val) {
      if (val) {
        this.selectedIds = [...this.ids];
      } else {
        this.selectedIds = [];
      }
    },
    // 取消保存：恢复各页面的初始位移参数
    onCancelSave() {
      // 恢复当前页数据
      this.setXYOffsetData(this.id, { ...this.initFormData });
      this.formData = { ...this.initFormData };
      // 恢复所有被选中页面的初始数据
      this.selectedIds.forEach(selectedId => {
        const index = this.ids.indexOf(selectedId);
        if (index > -1) {
          this.setXYOffsetData(selectedId, { ...this.idsInitFormData[index] });
        }
      });
      this.onClose();
    },
    // 确认保存：校验表单后对所有选中页面提交更新
    async onSubmit() {
      this.$refs.displacementForm.validate(async (valid) => {
        if (valid) {
          if (this.selectedIds.length === 0) {
            this.$message.error("请选择至少一页进行修改");
            return;
          }
          let loadingMessage = this.$message({
            message: "正在修改...",
            icon: "Loading",
            type: "warning",
            duration: 0,
          });
          this.isSaving = true;
          
          // 遍历选中页面进行更新
          for (let i = 0; i < this.selectedIds.length; i++) {
            let taskId = this.selectedIds[i];
            if (!taskId.includes('temp')) {
              if (this.useUpdateInterface) {
                // 调用update接口，传递pageSettings参数
                const payload = {
                  id: taskId,
                  pageSettings: JSON.stringify({
                    markZoom: this.markZoom,
                    fontSize: this.fontSize,
                    opacity: this.opacity
                  })
                };
                await this.$axios.post("/api/docCorrectConfig/update", payload);
              } else {
                // 调用updateOffset接口
                const payload = {
                  taskId: taskId,
                  offsetX: this.formData.xOffset,
                  offsetY: this.formData.yOffset
                };
                await this.$axios.post("/api/docCorrectTask/updateOffset", payload);
              }
            }
            // 更新store中的数据
            this.setXYOffsetData(taskId, { ...this.formData });
            store.setMarkZoomDataById(taskId, this.markZoom);
            store.setFontSizeDataById(taskId, this.fontSize);
            store.setOpacityDataById(taskId, this.opacity);
          }
          loadingMessage.close();
          this.isSaving = false;
          this.$message.success("修改成功");
          this.$emit('refresh');
          this.onClose();
        } else {
          this.$message.error("请正确填写所有字段");
        }
      });
    }
  }
};
</script>

<style scoped>
/* 确认按钮样式 - 使用最强的选择器 */
.xy-offset-dialog :deep(.el-button--primary),
.xy-offset-dialog :deep(.el-dialog__footer .el-button--primary) {
  background-color: #1677FF !important;
  border-color: #1677FF !important;
  color: #FFF !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2) !important;
}

.xy-offset-dialog :deep(.el-button--primary:hover) {
  background-color: #4a90ff !important;
  border-color: #4a90ff !important;
  color: #FFF !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4) !important;
}

.xy-offset-dialog :deep(.el-button--primary:active) {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15) !important;
}

/* 针对加载状态的按钮 */
.xy-offset-dialog :deep(.el-button--primary.is-loading) {
  background-color: #1677FF !important;
  border-color: #1677FF !important;
  color: #FFF !important;
}
</style>
