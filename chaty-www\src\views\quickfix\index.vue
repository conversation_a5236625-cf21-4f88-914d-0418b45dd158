<template>
    <div class="main-wrapper">
        <el-page-header class="header-bar" @back="goBack">
            <template #content>
                <div style="display: flex; align-items: center; gap: 24px;">
                    <div style="display: flex; align-items: center;">
                        <el-image src="/icon/16.png" class="left-icon"></el-image>
                        <el-text class="title">快速纠错</el-text>
                    </div>
                    <el-form inline ref="formRef" :model="formData" class="header-form" rules="formRules" style="margin-bottom: 0;">
                        <el-form-item label="已纠错试卷">
                            <el-select v-model="formData.templateId" placeholder="请输入试卷名称" clearable style="width: 200px"
                                filterable remote :remote-method="loadTempOptions" :loading="loadingTempOption">
                                <el-option v-for="item in tempOptions" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="纠错试卷">
                            <el-select v-model="formData.fixedId" placeholder="请输入试卷名称" clearable style="width: 200px"
                                filterable remote :remote-method="loadFixedOptions" :loading="loadingFixedOption">
                                <el-option v-for="item in fixedOptions" :key="item.id" :label="item.name"
                                    :value="item.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-tooltip placement="top" content="不要重复纠错">
                              <el-button class="quick-fix-button" :loading="fixing" @click="doFix">快速纠错</el-button>
                            </el-tooltip>
                        </el-form-item>
                    </el-form>
                </div>
            </template>
          <template #extra>
            <div style="font-size: 12px">Tip: 准确率1(批改失败视为本题批改结果为勾)  准确率2(批改失败视为本题批改结果与正确批改结果相反)</div>
          </template>
        </el-page-header>
        <div class="main-content">
            <el-table v-loading="loading" :data="tableData" style="height: 100%" empty-text="无数据" :border="false">
                <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center"
                    :fixed="column.prop === 'operations' ? 'right' : ''">
                    <template v-if="column.prop === 'operations'" v-slot="scope">
                        <el-space :size="5">
                            <el-link type="primary" @click="onDelete(scope.row.id)">删除</el-link>
                            <el-link type="primary" @click="doExport(scope.row)">导出</el-link>
                            <el-link type="primary" @click="showDetail(scope.row)">详情</el-link>
                        </el-space>
                    </template>
                    <template v-if="column.prop === 'templateName'" v-slot="scope">
                        <el-link type="primary" @click="toCorrectFile(scope.row.templateId)">
                            {{ scope.row.templateName }}
                        </el-link>
                    </template>
                    <template v-if="column.prop === 'fixedName'" v-slot="scope">
                        <el-link type="primary" @click="toCorrectFile(scope.row.fixedId)">
                            {{ scope.row.fixedName }}
                        </el-link>
                    </template>
                    <template v-if="column.prop === 'rightNum'" v-slot="scope">
                        {{ scope.row.totalNum - scope.row.fixedNum }}
                    </template>
                    <template v-if="column.prop === 'correctNum1'" v-slot="scope">
                      <div style="display: flex;align-items: center;width: 100%;justify-content: center">
                        <div>
                          {{ scope.row.totalNum === 0 || (scope.row?.fixedNum === undefined) ? '-' :
                            (((1 - (scope.row.correctNum) / scope.row.totalNum) * 100).toFixed(2) + '%')}}
                        </div>
                        <el-tooltip placement="top" content="准确率1(批改失败视为本题批改结果为勾)">
                          <el-icon style="margin-left: 5px"><Warning /></el-icon>
                        </el-tooltip>
                      </div>

                    </template>
                    <template v-if="column.prop === 'correctNum2'" v-slot="scope">
                      <div style="display: flex;align-items: center;width: 100%;justify-content: center">
                        <div>
                          {{ scope.row.totalNum === 0 || (scope.row?.fixedNum === undefined) ? '-' :
                            (((1 - scope.row.fixedNum / scope.row.totalNum) * 100).toFixed(2) + '%')}}
                        </div>
                        <el-tooltip placement="top" content="准确率2(批改失败视为本题批改结果与正确批改结果相反)">
                          <el-icon style="margin-left: 5px"><Warning /></el-icon>
                        </el-tooltip>
                      </div>

                    </template>
                  <template v-if="column.prop === 'identifyRatio'" v-slot="scope">
                        {{ scope.row?.identifyRatio !== undefined ? (scope.row.identifyRatio + '%') : '无' }}
                    </template>
                  <template v-if="column.prop === 'studentNumberRatio'" v-slot="scope">
                        {{ scope.row?.studentNumberRatio !== undefined ? (scope.row.studentNumberRatio + '%') : '无' }}
                    </template>
                  <template v-if="column.prop === 'failNum'" v-slot="scope">
                    {{ scope.row?.failNum ?? '无' }}
                  </template>
                  <template v-if="column.prop === 'stats'" v-slot="scope">
                    {{ scope.row?.stats ? ((JSON.parse(scope.row?.stats ?? '{}')?.averageTimeConsumption).toFixed(2) + 's') : '-' }}
                  </template>
                  <template v-if="column.prop === 'remark'" v-slot="scope">
                    <el-text style="cursor: pointer;text-decoration: underline" @click="editRemark(scope.row.id, scope.row?.remark)">
                      {{ scope.row?.remark || '点击修改' }}
                    </el-text>
                  </template>
                  <template v-else-if="column.prop === 'createTime'" v-slot="scope">
                    {{
                      $getFeiShuTimeFormat(scope.row?.createTime)
                    }}
                  </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="footer-bar">

            <el-pagination background
                           layout="sizes, prev, pager, next"
                           v-model:page-size="pageSize"
                           :page-sizes="[5, 6, 7, 8, 9, 10, 20, 50, 100]"
                           v-model:current-page="pageNumber"
                           :total="total"
                           @size-change="loadData"
                           @current-change="loadData" />
        </div>
        <Detail ref="detailRef" />
    </div>
</template>

<script>
import { useUserStore } from "@/store/index";
import { mapState } from "pinia";
import Detail from "./detail.vue";

export default {
    components: {
        Detail
    },
    data() {
        return {
            formData: {
                templateId: "",
                fixedId: "",
            },
            formRules: {
                templateId: [{ required: true, message: "请选择已纠错试卷", trigger: "blur" }],
                fixedId: [{ required: true, message: "请选择纠错试卷", trigger: "blur" }],
            },
            tableData: [],
            columns: [
                { label: "序号", type: "index", width: 100, align: "center" },
                { label: "已纠错试卷", prop: "templateName" },
                { label: "纠错试卷", prop: "fixedName" },
                { label: "批改失败数", prop: "failNum" },
                { label: "纠错数(包括批改失败)", prop: "fixedNum" },
                { label: "批改正确数", prop: "rightNum" },
                { label: "批改总数", prop: "totalNum" },
                { label: "姓名准确率", prop: "identifyRatio" },
                { label: "学号准确率", prop: "studentNumberRatio" },
                { label: "准确率1", prop: "correctNum1" },
                { label: "准确率2", prop: "correctNum2" },
                { label: "请求平均时间", prop: "stats" },
                { label: "备注", prop: "remark" },
                { label: "创建时间", prop: "createTime", width: 200 },
                { label: "操作", prop: "operations", width: 200 },
            ],
            pageNumber: 1,
            pageSize: 7,
            total: 0,
            loading: false,
            loadingTempOption: false,
            loadingFixedOption: false,
            tempOptions: [],
            fixedOptions: [],
            fixing: false,
        }
    },
    created() {
        this.loadData();
    },
    computed: {
        ...mapState(useUserStore, ["getUser"]),
    },
    methods: {
        editRemark(id, remark) {
          this.$prompt('请输入新的备注', '修改备注', {
            inputValue: remark ?? '',
            confirmButtonText: '修改',
            cancelButtonText: '取消',
          }).then(({value}) => {
            const param = {
              id: id,
              remark: value
            }
            this.$axios.post("/api/quickfix/update", param).then(res => {
              // 更新
              this.$message.success("修改成功！")
              this.loadData();
            })
          }).catch((e) => {
            // 用户点击取消时的处理逻辑
            this.$message.info("已取消修改");
          });
        },
        goBack() {
          this.$router.back();
        },
        loadData() {
            this.loading = true;
            this.$axios.post("/api/quickfix/page", {
                page: {
                    pageNumber: this.pageNumber,
                    pageSize: this.pageSize,
                }
            }).then(res => {
                this.tableData = res.data.records;
                this.total = res.data.total;
            }).finally(() => {
                this.loading = false;
            });
        },
        loadCorrectFile(name) {
            return this.$axios.post("/api/docCorrectFile/page", {
                name,
                page: {
                    pageNumber: 1,
                    pageSize: -1,
                }
            }).then(res => {
                return res.data.records;
            });
        },
        loadTempOptions(name) {
            if (!name || name === "") {
                return Promise.resolve([]);
            }
            this.loadingTempOption = true;
            this.loadCorrectFile(name).then(res => {
                this.tempOptions = res
            }).finally(() => {
                this.loadingTempOption = false;
            });
        },
        loadFixedOptions(name) {
            if (!name || name === "") {
                return Promise.resolve([]);
            }
            this.loadingFixedOption = true;
            this.loadCorrectFile(name).then(res => {
                this.fixedOptions = res
            }).finally(() => {
                this.loadingFixedOption = false;
            });
        },
        doFix() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                  this.fixing = true;
                    this.$axios.post("/api/quickfix/fix", this.formData).then(() => {
                        this.$message.success("纠错成功");
                        this.loadData();
                    }).finally(() => {
                        this.fixing = false;
                    });
                }
            });
        },
        toCorrectFile(id) {
            this.$router.push(`/markPapers/stepMarkPapers/${id}`);
        },
        onDelete(id) {
            this.$axios.get(`/api/quickfix/delete?id=${id}`).then(() => {
                this.$message.success("删除成功");
                this.loadData();
            });
        },
        doExport(row) {
            let loadingMessage = this.$message({
                message: "正在导出结果...",
                icon: "Loading",
                type: "warning",
                duration: 0,
            })
            this.$axios.get(`/api/quickfix/dwonload?id=${row.id}`).then(res => {
                return this.downloadFile(res.data.fileUrl, `${row.templateName} 纠错结果.pdf`);
            }).finally(() => {
                loadingMessage.close()
            })
        },
        downloadFile(url, name) {
            // 使用fetch获取文件内容
            return fetch(this.$fileserver.fileurl(url))
                .then(response => response.blob())
                .then(blob => {
                    // 如果需要下载，可以使用前面提到的下载代码
                    const a = document.createElement("a");
                    a.style.display = "none";
                    a.href = URL.createObjectURL(blob);
                    a.download = name;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                })
                .catch(error => {
                    console.error('发生错误:', error);
                });
        },
        showDetail(data) {
            this.$refs.detailRef.show({ data });
        },
    },
}
</script>

<style lang="scss" scoped>
.main-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header-bar {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        height: 48px;
        flex-shrink: 0;
        
        .left-icon {
            width: 28.28px;
            height: 22.89px;
            transform: scaleX(-1);
        }
        .title {
            font-weight: bold;
            font-size: 18px;
            color: #333333;
            letter-spacing: 0;
            margin-right: 19px;
        }
        .header-form {
            :deep(.el-form-item) {
                margin-bottom: 0;
            }
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 200px);
    }

    .footer-bar {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    // 快速纠错按钮自定义样式
    :deep(.quick-fix-button) {
        background-color: #1677FF !important;
        color: #FFF !important;
        border: none !important;
        font-weight: 700 !important;
        transition: all 0.3s ease !important;

        &:hover {
            background-color: #4a90ff !important;
            color: #FFF !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3) !important;
        }

        &:focus {
            background-color: #4a90ff !important;
            color: #FFF !important;
        }

        &:active {
            background-color: #1677FF !important;
            color: #FFF !important;
        }

        // 加载状态时保持样式
        &.is-loading {
            background-color: #1677FF !important;
            color: #FFF !important;
        }
    }
}
</style>