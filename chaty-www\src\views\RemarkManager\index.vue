<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchForm" :model="searchForm" class="header-form">
          <el-form-item label="评语内容">
            <el-input
                v-model="searchForm.keyword"
                placeholder="请输入评语内容关键字"
                clearable
                style="width: 260px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="loadData">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="success" @click="openDialog()">新增</el-button>
            <el-button type="warning" plain @click="randomPick">随机抽取</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <!-- 表格 -->
    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          empty-text="无数据"
      >
        <el-table-column type="index" label="序号" width="80" />

        <el-table-column prop="content" label="评语内容" min-width="420">
          <template #default="{ row }">
            <el-tooltip
                v-if="(row.content || '').length > 60"
                effect="dark"
                :content="row.content"
                placement="top"
            >
              <span>{{ row.content.slice(0, 60) + '...' }}</span>
            </el-tooltip>
            <span v-else>{{ row.content || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="hitProb" label="命中概率" width="120" align="center" />

        <el-table-column prop="createTime" label="创建时间" width="200" />
        <el-table-column prop="updateTime" label="更新时间" width="200" />

        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="openDialog(row)">编辑</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="isEdit ? '编辑评语' : '新增评语'" v-model="dialogVisible" width="700px">
      <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogRules" label-width="120px">
        <el-form-item label="评语内容" prop="content">
          <el-input
              v-model="dialogForm.content"
              type="textarea"
              :rows="6"
              placeholder="请输入评语内容"
              maxlength="5000"
              show-word-limit
          />
        </el-form-item>

        <el-form-item label="命中概率" prop="hitProb">
          <el-input-number v-model="dialogForm.hitProb" :min="0" :step="1" />
          <span class="tip">（值越大，被抽中的概率越高；0 表示不会被抽中）</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submitDialog">确定</el-button>
      </template>
    </el-dialog>

    <!-- 随机抽取结果 -->
    <el-dialog title="随机抽取结果" v-model="randomDialogVisible" width="700px">
      <div v-if="randomItem">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="ID">{{ randomItem.id }}</el-descriptions-item>
          <el-descriptions-item label="命中概率">{{ randomItem.hitProb }}</el-descriptions-item>
          <el-descriptions-item label="评语内容">
            <div style="white-space: pre-wrap;">{{ randomItem.content }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="randomDialogVisible=false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RemarkManager',
  data() {
    return {
      // 查询
      searchForm: { keyword: '' },
      loading: false,

      // 列表
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,

      // 新增/编辑
      dialogVisible: false,
      isEdit: false,
      dialogLoading: false,
      dialogFormRef: null,
      dialogForm: {
        id: null,
        content: '',
        hitProb: 1
      },
      dialogRules: {
        content: [{ required: true, message: '请输入评语内容', trigger: 'blur' }],
        hitProb: [{ required: true, message: '请输入命中概率', trigger: 'blur' }]
      },

      // 随机抽取
      randomDialogVisible: false,
      randomItem: null
    };
  },
  methods: {
    goBack() {
      this.$router.back();
    },

    // 加载列表
    loadData() {
      this.loading = true;
      this.$axios.get('/api/remark/page', {
        params: {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
          keyword: this.searchForm.keyword || ''
        }
      })
          .then(res => {
            const d = res?.data || {};
            this.tableData = d.records || [];
            this.total = d.total || 0;
          })
          .finally(() => {
            this.loading = false;
          });
    },

    reset() {
      this.searchForm.keyword = '';
      this.pageNumber = 1;
      this.loadData();
    },

    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },

    // 打开新增/编辑
    openDialog(row = null) {
      if (row) {
        this.isEdit = true;
        this.dialogForm = {
          id: row.id,
          content: row.content || '',
          hitProb: typeof row.hitProb === 'number' ? row.hitProb : 1
        };
      } else {
        this.isEdit = false;
        this.dialogForm = {
          id: null,
          content: '',
          hitProb: 1
        };
      }
      this.dialogVisible = true;
    },

    // 保存
    submitDialog() {
      this.$refs.dialogFormRef.validate(valid => {
        if (!valid) return;
        this.dialogLoading = true;
        const api = this.isEdit ? '/api/remark/update' : '/api/remark/add';
        const payload = {
          id: this.dialogForm.id,
          content: this.dialogForm.content,
          hitProb: this.dialogForm.hitProb
        };
        this.$axios.post(api, payload)
            .then(() => {
              this.$message.success(this.isEdit ? '更新成功' : '新增成功');
              this.dialogVisible = false;
              this.loadData();
            })
            .finally(() => {
              this.dialogLoading = false;
            });
      });
    },

    // 删除（后端为 REST Delete /api/remark/{id}）
    onDelete(id) {
      this.$confirm('确认删除该评语？', '警告', { type: 'warning' })
          .then(() => this.$axios.delete(`/api/remark/${id}`))
          .then(() => {
            this.$message.success('删除成功');
            this.loadData();
          })
          .catch(() => {});
    },

    // 加权随机抽取
    randomPick() {
      this.$axios.get('/api/remark/random')
          .then(res => {
            const item = res?.data;
            if (!item) {
              this.$message.info('没有可抽取的评语（可能命中概率均为 0）');
              return;
            }
            this.randomItem = item;
            this.randomDialogVisible = true;
          });
    }
  },
  created() {
    this.loadData();
  }
};
</script>

<style lang="scss" scoped>
.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
