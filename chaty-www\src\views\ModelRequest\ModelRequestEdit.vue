<template>
  <div class="edit-two-pane">
    <!-- 顶部 -->
    <el-page-header class="page-header" @back="goBack">
      <template #content>
        <div class="page-title">
          <el-text class="title">{{ isEdit ? '编辑模型请求' : '新增模型请求' }}</el-text>
          <el-tag v-if="form.id" type="info" class="id-tag">ID: {{ form.id }}</el-tag>
        </div>
      </template>
      <template #extra>
        <div class="header-actions">
          <el-button @click="save(true)" :loading="saving">仅保存</el-button>
          <el-button
            class="save-return-button"
            @click="save(false)"
            :loading="saving"
            style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
            @mouseenter="handleSaveReturnMouseEnter"
            @mouseleave="handleSaveReturnMouseLeave">
            保存并返回
          </el-button>
          <el-divider direction="vertical" />
          <el-button v-if="isEdit" type="success" @click="handleCopy">复制为新建</el-button>
          <el-button v-if="isEdit" type="danger" @click="handleDelete">删除</el-button>
        </div>
      </template>
    </el-page-header>

    <!-- 主体：左右两栏 -->
    <div class="pane-grid">
      <!-- 左侧：模型参数（可滚动） -->
      <section class="left-pane">
        <el-card shadow="never" class="pane-card elev" :body-style="{padding:'10px'}">
          <div class="sticky-subtitle">
            <el-text class="subtitle">模型参数</el-text>
          </div>

          <el-scrollbar class="pane-scroll">
            <el-form ref="editForm" :model="form" :rules="rules" label-width="150px" class="form-compact">
              <el-divider content-position="left">基础信息</el-divider>
              <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称" clearable />
              </el-form-item>

              <el-form-item label="模型值" prop="modelValue">
                <el-autocomplete
                    v-model="form.modelValue"
                    :fetch-suggestions="fetchModelValueSuggestions"
                    placeholder="请输入或选择模型值"
                    @change="onModelValueChange"
                    clearable
                />
              </el-form-item>

              <!-- 题型：必选 + 提交 -->
              <el-form-item label="题型" prop="questionType">
                <el-select
                    v-model="form.questionType"
                    placeholder="请选择题型"
                    filterable clearable :loading="loadingQTypes" style="width: 100%;"
                >
                  <el-option v-for="qt in questionTypeOptions" :key="qt" :label="qt" :value="qt" />
                </el-select>
              </el-form-item>

              <el-divider content-position="left">批改策略</el-divider>
              <el-form-item label="禁止批改">
                <el-switch v-model="form.disabled" active-text="开启" inactive-text="关闭" />
              </el-form-item>
              <el-alert
                  v-if="form.disabled"
                  title="已开启【禁止批改】：其余参数将被隐藏并在保存时忽略"
                  type="warning" show-icon :closable="false" class="block-alert"
              />

              <template v-if="!form.disabled">
                <el-form-item label="类型选择" prop="typeSelection">
                  <el-radio-group v-model="form.typeSelection">
                    <el-radio :label="'jsonobject'">JsonObject</el-radio>
                    <el-radio :label="'jsonschema'">JsonSchema</el-radio>
                    <el-radio :label="'text'">Text</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-divider content-position="left">两轮问询</el-divider>
                <el-form-item label="普通题目两轮询问">
                  <el-switch v-model="form.enableNormalQsTwoRequest" active-text="开启" inactive-text="关闭" />
                </el-form-item>

                <template v-if="form.enableNormalQsTwoRequest">
                  <el-form-item label="第二轮开启图片">
                    <el-switch v-model="form.isSecondRoundUseImage" active-text="开启" inactive-text="关闭" />
                  </el-form-item>
                  <el-form-item label="第二轮使用JSON比对">
                    <el-switch v-model="form.isSecondRoundJsonComparison" active-text="开启" inactive-text="关闭" />
                  </el-form-item>

                  <el-form-item label="第一轮提示词类型">
                    <el-select v-model="form.firstRoundPromptType" style="width: 100%">
                      <el-option label="user" value="user" />
                      <el-option label="system" value="system" />
                      <el-option label="mixed" value="mixed" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="第二轮提示词类型" v-if="!form.isSecondRoundJsonComparison">
                    <el-select v-model="form.secondRoundPromptType" style="width: 100%">
                      <el-option label="user" value="user" />
                      <el-option label="system" value="system" />
                      <el-option label="mixed" value="mixed" />
                    </el-select>
                  </el-form-item>
                </template>

                <template v-else>
                  <el-form-item label="一轮提示词类型">
                    <el-select v-model="form.singleRoundPromptType" style="width: 100%">
                      <el-option label="user" value="user" />
                      <el-option label="system" value="system" />
                      <el-option label="mixed" value="mixed" />
                    </el-select>
                  </el-form-item>
                </template>

                <el-divider content-position="left">图像&生成</el-divider>
                <el-form-item label="图像增强">
                  <el-switch v-model="form.enableImageEnhancement" active-text="开启" inactive-text="关闭" />
                </el-form-item>
                <el-form-item label="temperature">
                  <el-slider v-model="form.temperature" :min="0" :max="2" :step="0.01" class="slider" />
                  <span class="slider-val">{{ form.temperature }}</span>
                  <span class="slider-hint">(0-2)</span>
                </el-form-item>
                <el-form-item label="top_p">
                  <el-slider v-model="form.top_p" :min="0" :max="1" :step="0.01" class="slider" />
                  <span class="slider-val">{{ form.top_p }}</span>
                  <span class="slider-hint">(0-1)</span>
                </el-form-item>
                <el-form-item label="thinking">
                  <el-select
                      v-model="form.thinking" style="width: 100%;" :disabled="!isThinkingSupported"
                      @click.native="handleThinkingClick"
                  >
                    <el-option label="disabled" value="disabled" />
                    <el-option label="enabled" value="enabled" />
                    <el-option label="auto" value="auto" />
                  </el-select>
                </el-form-item>

                <el-form-item label="其他参数" prop="content">
                  <el-input type="textarea" rows="4" v-model="form.content" placeholder="请输入其他参数(应为JSON)" />
                </el-form-item>
              </template>

              <el-divider content-position="left">通用</el-divider>
              <el-form-item label="权重" prop="weight">
                <el-input-number v-model="form.weight" :min="0" style="width: 100%;" />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入备注" />
              </el-form-item>
            </el-form>
          </el-scrollbar>
        </el-card>
      </section>

      <!-- 右侧：提示词管理（可滚动） -->
      <section class="right-pane">
        <el-card
            shadow="never" class="pane-card elev" :body-style="{padding:'10px'}"
            v-loading="autoSyncLoading" element-loading-text="同步中..."
            element-loading-background="rgba(255,255,255,0.85)"
        >
          <div class="sticky-subtitle right-header">
            <div class="subtitle">提示词管理</div>
            <div class="right-tools">
              <el-button link type="danger" @click="clearAllPrompts" :disabled="pm.promptEntries.length===0">清空</el-button>
              <el-tag v-if="autoSyncLoading" type="info" effect="dark">同步中...</el-tag>
            </div>
          </div>

          <el-scrollbar class="pane-scroll">
            <!-- 手动新增（仅本地） -->
            <el-form inline class="add-form">
              <el-form-item label="选择类别">
                <el-select
                    v-model="pm.newType" placeholder="请选择类别" style="width: 220px"
                    filterable clearable @change="onTypeChange"
                >
                  <el-option v-for="type in categories" :key="type" :label="type" :value="type" />
                </el-select>
              </el-form-item>

              <el-form-item label="选择中文名称">
                <el-select
                    v-model="pm.newDefaultId" placeholder="请选择中文名称" style="width: 360px"
                    :disabled="!pm.newType" filterable clearable
                >
                  <el-option
                      v-for="optId in filteredDefaultIds" :key="optId"
                      :label="pm.defaultById[optId]?.name || '-'" :value="optId"
                  />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  class="add-unsaved-button"
                  @click="addManualPrompt"
                  :disabled="!pm.newDefaultId"
                  style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
                  @mouseenter="handleAddUnsavedMouseEnter"
                  @mouseleave="handleAddUnsavedMouseLeave">
                  新增（未保存）
                </el-button>
              </el-form-item>
            </el-form>

            <el-divider />

            <!-- 提示词列表 -->
            <div
                v-for="item in pm.promptEntries"
                :key="item.clientId || item.id"
                class="prompt-card"
            >
              <div class="prompt-card__title">
                <div class="title-left">
                  <span class="prompt-name">{{ item.name }}</span>
                  <el-tag v-if="isStringTag(item)" type="primary" size="small" class="ml8">string</el-tag>
                  <el-tag v-else-if="isJsonTag(item)" type="danger" size="small" class="ml8">json</el-tag>
                  <el-tag v-if="item.autoManaged" type="success" size="small" class="ml8" effect="plain">自动</el-tag>
                  <el-tag v-if="!item.isPersisted" type="warning" size="small" class="ml8" effect="plain">未保存</el-tag>
                </div>
                <div class="title-right">
                  <el-text type="info" size="small">
                    题型：{{ item.questionType || '-' }} ｜ 阶段：{{ item.majorStageType || '-' }} ｜ 角色：{{ item.role || '-' }} ｜ 图片：{{ item.hasImage ? '是' : '否' }}
                  </el-text>
                </div>
              </div>

              <el-input
                  type="textarea" v-model="item.value" :autosize="{ minRows: 4 }"
                  class="prompt-input" placeholder="请输入提示词内容" @input="markDirty(item)"
              />

              <div class="prompt-actions">
                <el-button type="warning" @click="resetToDefaultLocal(item)" :disabled="!canReset(item)">
                  重置为默认
                </el-button>
                <el-button type="danger" @click="removeLocal(item)">删除（本地）</el-button>
              </div>
            </div>
          </el-scrollbar>
        </el-card>
      </section>
    </div>
  </div>
</template>

<script>
/**
 * ModelRequestEdit.vue — 修正版
 * - questionType：必填，提交至 add / update（GET /api/prompts/questionTypes 拉取候选）
 * - 模型值：未开启“禁止批改”时必填
 * - 提示词：本地增删改，保存时服务端先清空再全量新增；自动联动仅替换“自动且未修改”的临时项
 */
const thinkingModels = [
  'doubao-seed-1-6-250615',
  'doubao-seed-1-6-flash-250615'
]

let TEMP_ID_SEQ = 1
const newTempId = () => `tmp-${Date.now()}-${TEMP_ID_SEQ++}`

export default {
  name: 'ModelRequestEdit',
  data() {
    return {
      saving: false,
      isEdit: false,
      autoSyncLoading: false,
      formWatchTimer: null,

      loadingQTypes: false,
      questionTypeOptions: [],

      form: {
        id: null,
        name: '',
        modelValue: '',
        jsonobject: false,
        jsonschema: false,
        typeSelection: 'text',
        content: '',
        remark: '',
        weight: 0,
        temperature: 1,
        top_p: 0.7,
        thinking: 'disabled',
        enableNormalQsTwoRequest: false,
        isSecondRoundUseImage: false,
        isSecondRoundJsonComparison: false,
        firstRoundPromptType: 'user',
        secondRoundPromptType: 'user',
        singleRoundPromptType: 'user',
        enableImageEnhancement: true,
        disabled: false,
        questionType: '' // 必填 & 提交
      },

      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        questionType: [{ required: true, message: '题型为必填', trigger: 'change' }],
        modelValue: [{ validator: (r, v, cb) => this.validateModelValue(v, cb), trigger: ['blur', 'change'] }],
        content: [{ validator: (r, v, cb) => this.validateJson(v, cb), trigger: 'blur' }]
      },

      defaultTemperature: 1,
      defaultTopP: 0.7,

      isSyncingFromContent: false,
      isSyncingFromControl: false,

      pm: {
        defaultList: [],
        defaultById: {},
        promptEntries: [],
        newType: '',
        newDefaultId: ''
      },
      maxEmptyWight: 0
    }
  },

  computed: {
    isThinkingSupported() { return thinkingModels.includes(this.form.modelValue) },
    categories() { return Array.from(new Set(this.pm.defaultList.map(d => d.category || '未分类'))) },
    filteredDefaultIds() {
      const arr = this.pm.defaultList.filter(d => (d.category || '未分类') === this.pm.newType)
      arr.sort((a, b) => (a.name || '').localeCompare(b.name || '', 'zh-CN'))
      return arr.map(d => d.id)
    }
  },

  async created() {
    await this.initPage()
  },

  methods: {
    goBack() {
      const prevUrl = window.history.state?.back || '';
      if (prevUrl.includes('/markPapers/upload')) {
        this.$router.go(-2);
      } else {
        this.$router.back();
      }
    },
    /* ---------------- 初始化 ---------------- */
    async initPage() {
      await this.$axios.get('/api/model-request/getMaxEmptyWeight').then(res => { this.maxEmptyWight = res.data || 0 })
      // 可从上页透传缓存，这里仅演示从 sessionStorage 读取
      const cached = sessionStorage.getItem('model-request:edit-cache')
      let row = null
      try { row = cached ? JSON.parse(cached) : null } catch { row = null }
      this.isEdit = !!(row && row.id)

      if (this.isEdit) {
        this.form = {
          ...this.form,
          ...row,
          typeSelection: row.jsonobject ? 'jsonobject' : (row.jsonschema ? 'jsonschema' : 'text'),
          firstRoundPromptType: row.firstRoundPromptType || 'user',
          secondRoundPromptType: row.secondRoundPromptType || 'user',
          singleRoundPromptType: row.singleRoundPromptType || 'user',
          questionType: row.questionType || ''
        }
      } else {
        const obj = thinkingModels.includes(this.form.modelValue) ? { thinking: { type: 'disabled' } } : {}
        this.form.content = JSON.stringify(obj, null, 2)
        this.form.weight = this.maxEmptyWight;
      }

      await this.fetchQuestionTypes()
      await this.fetchDefaultDefs()
      if (this.form.id) await this.fetchOverrides()
    },

    /* ---------------- 拉取题型（修复：新增此方法） ---------------- */
    async fetchQuestionTypes() {
      try {
        this.loadingQTypes = true
        const res = await this.$axios.get('/api/prompts/questionTypes')
        const list = Array.isArray(res?.data) ? res.data : (res?.data?.data || [])
        this.questionTypeOptions = (list || []).filter(Boolean)
      } catch (e) {
        console.error(e)
        this.questionTypeOptions = []
      } finally {
        this.loadingQTypes = false
      }
    },

    /* ---------------- 校验 ---------------- */
    validateJson(value, callback) {
      if (this.form.disabled || !value) return callback()
      try { JSON.parse(value); callback() } catch { callback(new Error('其他参数必须为合法的 JSON 字符串')) }
    },
    validateModelValue(value, callback) {
      if (!this.form.disabled && !value) return callback(new Error('模型值为必填'))
      callback()
    },

    /* ---------------- 保存 / 复制 / 删除 ---------------- */
    async save(stay = false) {
      this.$refs.editForm.validate(async valid => {
        if (!valid) return
        this.saving = true
        try {
          // 保存模型（questionType 一并提交）
          const payload = this.buildSubmitPayload()
          const api = this.isEdit ? '/api/model-request/update' : '/api/model-request/add'
          const res = await this.$axios.post(api, payload)
          if (!this.isEdit) {
            const saved = res?.data || {}
            this.form.id = saved.id || saved.data?.id || null
            this.isEdit = !!this.form.id
          }

          // 提示词：先清空后新增
          await this.replaceAllPromptsOnSave()

          this.$message.success('保存成功')
          if (stay) {
            if (this.form.id) await this.initPromptManager()
          } else {
            this.$router.replace({ name: 'modelRequest' })
          }
        } catch (e) {
          console.error(e); this.$message.error('保存失败，请稍后再试')
        } finally {
          this.saving = false
        }
      })
    },
    async replaceAllPromptsOnSave() {
      if (!this.form.id) return
      await this.$axios.post('/api/prompts/deleteByModelSettingId', null, { params: { id: this.form.id } }).catch(() => {})
      const toAdd = this.pm.promptEntries.map(p => ({
        description: p.description || '',
        name: p.name || '',
        isMajorType: !!p.isMajorType,
        questionType: p.questionType || '',
        category: p.type || '',
        promptContent: p.value || '',
        majorStageType: p.majorStageType || '',
        tags: p.tags || 'string',
        role: p.role || '',
        hasImage: !!p.hasImage,
        modelRequestId: this.form.id
      }))
      if (toAdd.length) await Promise.allSettled(toAdd.map(payload => this.$axios.post('/api/prompts/add', payload)))
      await this.fetchOverrides()
    },
    handleCopy() {
      const now = new Date()
      const pad = n => n.toString().padStart(2, '0')
      const ts = `${now.getFullYear()}${pad(now.getMonth()+1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}`
      const newName = `${this.form.name || '未命名'}_${ts}`
      const payload = this.buildSubmitPayloadForCopy(newName)
      this.$confirm(`确定复制 "${this.form.name || '未命名'}" 为新记录吗？`, '复制确认', { type: 'warning' })
          .then(() => this.$axios.post('/api/model-request/add', payload))
          .then(() => { this.$message.success('复制并新增成功'); this.$router.replace({ name: 'modelRequest' }) })
          .catch(() => {})
    },
    handleDelete() {
      if (!this.form.id) return
      this.$confirm('确认删除该记录？', '警告', { type: 'warning' })
          .then(() => this.$axios.get(`/api/model-request/delete?id=${this.form.id}`))
          .then(() => { this.$message.success('删除成功'); this.$router.replace({ name: 'modelRequest' }) })
          .catch(() => {})
    },

    /* ---------------- 提交载荷 ---------------- */
    buildSubmitPayload() {
      const payload = { ...this.form }
      payload.jsonobject = (payload.typeSelection === 'jsonobject')
      payload.jsonschema = (payload.typeSelection === 'jsonschema')

      if (payload.disabled) {
        Object.assign(payload, {
          jsonobject: false, jsonschema: false, typeSelection: 'text',
          enableNormalQsTwoRequest: false, isSecondRoundUseImage: false, isSecondRoundJsonComparison: false,
          enableImageEnhancement: false,
          firstRoundPromptType: 'user', secondRoundPromptType: 'user', singleRoundPromptType: 'user',
          content: '', temperature: this.defaultTemperature, top_p: this.defaultTopP, thinking: 'disabled'
        })
      } else {
        if (!payload.enableNormalQsTwoRequest) {
          payload.isSecondRoundUseImage = false
          payload.isSecondRoundJsonComparison = false
        }
      }
      return payload
    },
    buildSubmitPayloadForCopy(newName) {
      const p = this.buildSubmitPayload()
      return { ...p, id: null, name: newName }
    },

    /* ---------------- 默认/覆盖提示词 ---------------- */
    async initPromptManager() {
      await this.fetchDefaultDefs()
      await this.fetchOverrides()
    },
    async fetchDefaultDefs() {
      const dto = { modelRequestId: null, page: { pageNumber: 1, pageSize: 5000, searchCount: false } }
      const res = await this.$axios.post('/api/prompts/page', dto)
      const page = res.data?.data || res.data
      const records = page?.records || []
      this.pm.defaultList = records.map(r => ({
        id: r.id,
        name: r.name || r.description || '',
        description: r.description || '',
        category: r.category || '未分类',
        isMajorType: !!r.isMajorType,
        questionType: r.questionType || '',
        majorStageType: r.majorStageType || '',
        role: r.role || '',
        hasImage: !!r.hasImage,
        tags: r.tags || 'string',
        promptContent: r.promptContent || ''
      }))
      const map = {}; this.pm.defaultList.forEach(d => { map[d.id] = d }); this.pm.defaultById = map
    },
    async fetchOverrides() {
      if (!this.form.id) return
      const dto = { modelRequestId: this.form.id, page: { pageNumber: 1, pageSize: 5000, searchCount: false } }
      const res = await this.$axios.post('/api/prompts/page', dto)
      const page = res.data?.data || res.data
      const records = page?.records || []
      this.pm.promptEntries = records.map(r => ({
        id: r.id,
        isPersisted: true,
        autoManaged: false,
        originalValue: r.promptContent || '',
        dirty: false,
        name: r.name || r.description || '',
        type: r.category || '未分类',
        value: r.promptContent || '',
        description: r.description || '',
        isMajorType: !!r.isMajorType,
        questionType: r.questionType || '',
        majorStageType: r.majorStageType || '',
        role: r.role || '',
        hasImage: !!r.hasImage,
        tags: r.tags || 'string'
      }))
    },

    /* ---------------- 提示词：手动新增/删除/重置（本地） ---------------- */
    onTypeChange() { this.pm.newDefaultId = '' },
    addManualPrompt() {
      if (!this.pm.newDefaultId) return
      const def = this.pm.defaultById[this.pm.newDefaultId]
      if (!def) return this.$message.error('未找到默认模板')

      const clientId = newTempId()
      this.pm.promptEntries.push({
        clientId, isPersisted: false, autoManaged: false,
        originalValue: def.promptContent || '', dirty: false,
        name: def.name || '', type: def.category || '未分类',
        value: def.promptContent || '',
        description: def.description || '',
        isMajorType: !!def.isMajorType,
        questionType: def.questionType || '',
        majorStageType: def.majorStageType || '',
        role: def.role || '',
        hasImage: !!def.hasImage,
        tags: def.tags || 'string'
      })
      this.$message.success('已新增（未保存）')
      this.pm.newDefaultId = ''
    },
    markDirty(item) {
      if (!item.isPersisted) item.dirty = item.value !== (item.originalValue || '')
    },
    removeLocal(item) {
      const key = item.id ?? item.clientId
      this.pm.promptEntries = this.pm.promptEntries.filter(e => (e.id ?? e.clientId) !== key)
      this.$message.success('已删除（本地）')
    },
    clearAllPrompts() {
      this.$confirm('确认清空当前所有提示词？仅本地删除，保存后才会同步到服务端。', '清空确认', { type: 'warning' })
          .then(() => { this.pm.promptEntries = []; this.$message.success('已清空（本地）') })
          .catch(() => {})
    },

    /* ---------------- 重置为默认（本地） ---------------- */
    canReset(item) { return !!this.findDefaultForItem(item) },
    findDefaultForItem(item) {
      const qt = item.questionType || ''
      const stage = item.majorStageType || ''
      const role = item.role || ''
      const hasImage = !!item.hasImage
      const cat = item.type || ''
      return this.pm.defaultList.find(d =>
          (d.questionType || '') === qt &&
          (d.majorStageType || '') === stage &&
          (d.role || '') === role &&
          !!d.hasImage === hasImage &&
          (d.category || '') === cat
      )
    },
    resetToDefaultLocal(item) {
      const def = this.findDefaultForItem(item)
      if (!def) return
      item.value = def.promptContent || ''
      if (!item.isPersisted) { item.originalValue = item.value; item.dirty = false }
      this.$message.success('已重置（本地）')
    },

    /* ---------------- 标签辅助 ---------------- */
    isStringTag(item) {
      const text = (item.name || '').toString()
      return text.includes('提示') || text.includes('模板') || item.type === '普通题目-大题类型' || item.type === '标准卷批注'
    },
    isJsonTag(item) { return (item.name || '').toString().includes('格式') },

    /* ---------------- 自动联动（仅本地；保留用户已改动的未保存项） ---------------- */
    async autoSyncPromptsByForm() {
      if (this.form.disabled) return
      try {
        this.autoSyncLoading = true

        // 1) 保留：已保存 / 手动新增 / 自动但已修改
        const keep = []
        for (const e of this.pm.promptEntries) {
          if (e.isPersisted || !e.autoManaged || (e.autoManaged && e.dirty)) keep.push(e)
        }

        // 2) 请求建议（id 可为 null，仅用于计算）
        const reqPayload = {
          modelRequestId: this.form.id || null,
          questionType: this.form.questionType,
          enableNormalQsTwoRequest: this.form.enableNormalQsTwoRequest,
          isSecondRoundUseImage: this.form.isSecondRoundUseImage,
          isSecondRoundJsonComparison: this.form.isSecondRoundJsonComparison,
          singleRoundPromptType: this.form.singleRoundPromptType,
          firstRoundPromptType: this.form.firstRoundPromptType,
          secondRoundPromptType: this.form.secondRoundPromptType
        }
        const res = await this.$axios.post('/api/prompts/getPromptsFromModelSettingAuto', reqPayload)
        const list = Array.isArray(res?.data) ? res.data : (res?.data?.data || [])
        const suggestions = (list || []).filter(Boolean)

        // 3) 去重合并
        const keyOf = (x) => [
          (x.name || '').trim(),
          (x.category || x.type || '').trim(),
          (x.questionType || '').trim(),
          (x.majorStageType || '').trim(),
          (x.role || '').trim(),
          (x.hasImage ? '1' : '0')
        ].join('#')

        const existing = new Set(keep.map(keyOf))
        const autos = []
        for (const p of suggestions) {
          const tmp = {
            clientId: newTempId(),
            isPersisted: false,
            autoManaged: true,
            originalValue: p.promptContent || '',
            dirty: false,
            name: p.name || '',
            type: p.category || '自动生成',
            value: p.promptContent || '',
            description: p.description || '',
            isMajorType: !!p.isMajorType,
            questionType: p.questionType || '',
            majorStageType: p.majorStageType || '',
            role: p.role || '',
            hasImage: !!p.hasImage,
            tags: p.tags || 'string'
          }
          const k = keyOf(tmp)
          if (!existing.has(k)) { existing.add(k); autos.push(tmp) }
        }

        this.pm.promptEntries = [...keep, ...autos]
      } catch (e) {
        console.error(e); this.$message.error('根据模型参数生成提示词失败')
      } finally {
        this.autoSyncLoading = false
      }
    },

    /* ---------------- 模型值联想 ---------------- */
    onModelValueChange() { this.syncContentFromControl() },
    fetchModelValueSuggestions(queryString, cb) {
      this.$axios.post('/api/model-request/selectPage', {
        page: { pageNumber: 1, pageSize: 50, searchCount: false }, name: '', remark: ''
      }).then(res => {
        const values = Array.from(new Set((res.data.records || []).map(r => r.modelValue)))
        const suggestions = values.filter(v => (v || '').includes(queryString)).map(v => ({ value: v }))
        cb(suggestions)
      })
    },

    /* ---------------- content 同步 ---------------- */
    handleThinkingClick(e) {
      if (!this.isThinkingSupported) { this.$message.info('本模型不支持本参数'); e.stopPropagation() }
    },
    syncContentFromControl() {
      if (this.form.disabled) return
      this.isSyncingFromControl = true
      const { thinking, temperature, top_p, modelValue } = this.form
      let obj = {}
      try { obj = this.form.content ? JSON.parse(this.form.content) : {} } catch { obj = {} }
      if (temperature !== this.defaultTemperature) obj.temperatrue = temperature; else delete obj.temperatrue
      if (top_p !== this.defaultTopP) obj.top_p = top_p; else delete obj.top_p
      if (thinkingModels.includes(modelValue)) obj.thinking = { type: thinking }; else delete obj.thinking
      this.form.content = Object.keys(obj).length === 0 ? '' : JSON.stringify(obj, null, 2)
      this.isSyncingFromControl = false
    },
    syncControlFromContent(val) {
      if (this.form.disabled) return
      this.isSyncingFromContent = true
      try {
        const obj = val ? JSON.parse(val) : {}
        if (obj && typeof obj === 'object') {
          this.form.thinking = (obj.thinking && obj.thinking.type) ? obj.thinking.type : 'disabled'
          this.form.top_p = ('top_p' in obj) ? obj.top_p : this.defaultTopP
          this.form.temperature = ('temperatrue' in obj) ? obj.temperatrue : this.defaultTemperature
        }
      } catch {
        this.form.thinking = 'disabled'
        this.form.top_p = this.defaultTopP
        this.form.temperature = this.defaultTemperature
      }
      this.isSyncingFromContent = false
    },

    /* ---------------- 默认集合辅助 ---------------- */
    async initPromptManager() {
      await this.fetchDefaultDefs()
      await this.fetchOverrides()
    },
    async fetchDefaultDefs() {
      const dto = { modelRequestId: null, page: { pageNumber: 1, pageSize: 5000, searchCount: false } }
      const res = await this.$axios.post('/api/prompts/page', dto)
      const page = res.data?.data || res.data
      const records = page?.records || []
      this.pm.defaultList = records.map(r => ({
        id: r.id,
        name: r.name || r.description || '',
        description: r.description || '',
        category: r.category || '未分类',
        isMajorType: !!r.isMajorType,
        questionType: r.questionType || '',
        majorStageType: r.majorStageType || '',
        role: r.role || '',
        hasImage: !!r.hasImage,
        tags: r.tags || 'string',
        promptContent: r.promptContent || ''
      }))
      const map = {}; this.pm.defaultList.forEach(d => { map[d.id] = d }); this.pm.defaultById = map
    },
    async fetchOverrides() {
      if (!this.form.id) return
      const dto = { modelRequestId: this.form.id, page: { pageNumber: 1, pageSize: 5000, searchCount: false } }
      const res = await this.$axios.post('/api/prompts/page', dto)
      const page = res.data?.data || res.data
      const records = page?.records || []
      this.pm.promptEntries = records.map(r => ({
        id: r.id,
        isPersisted: true,
        autoManaged: false,
        originalValue: r.promptContent || '',
        dirty: false,
        name: r.name || r.description || '',
        type: r.category || '未分类',
        value: r.promptContent || '',
        description: r.description || '',
        isMajorType: !!r.isMajorType,
        questionType: r.questionType || '',
        majorStageType: r.majorStageType || '',
        role: r.role || '',
        hasImage: !!r.hasImage,
        tags: r.tags || 'string'
      }))
    },
    handleSaveReturnMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleSaveReturnMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleAddUnsavedMouseEnter(event) {
      const button = event.target;
      if (!button.disabled) {
        button.style.backgroundColor = '#4a90ff';
        button.style.borderColor = '#4a90ff';
        button.style.transform = 'translateY(-2px)';
        button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
      }
    },
    handleAddUnsavedMouseLeave(event) {
      const button = event.target;
      if (!button.disabled) {
        button.style.backgroundColor = '#1677FF';
        button.style.borderColor = '#1677FF';
        button.style.transform = 'translateY(0)';
        button.style.boxShadow = 'none';
      }
    }
  },

  watch: {
    // 表单变更自动联动（去抖 500ms）
    form: {
      deep: true,
      handler() {
        if (this.form.disabled) return
        clearTimeout(this.formWatchTimer)
        this.formWatchTimer = setTimeout(() => this.autoSyncPromptsByForm(), 500)
      }
    },
    // content 同步
    'form.modelValue'() { this.syncContentFromControl() },
    'form.temperature'() { if (!this.isSyncingFromContent) this.syncContentFromControl() },
    'form.top_p'() { if (!this.isSyncingFromContent) this.syncContentFromControl() },
    'form.thinking'() { if (!this.isSyncingFromContent) this.syncContentFromControl() },
    'form.content'(val) { if (!this.isSyncingFromControl) this.syncControlFromContent(val) },

    // 两轮联动
    'form.enableNormalQsTwoRequest'(val) {
      if (this.form.disabled) return
      if (!val) {
        this.form.isSecondRoundUseImage = false
        this.form.isSecondRoundJsonComparison = false
      }
    }
  }
}
</script>

<style scoped>
/* 布局与面板 */
.edit-two-pane { display: flex; flex-direction: column; height: 100vh; background: #f6f7f9; }
.page-header { padding: 8px 0 10px; background: transparent; border-bottom: 1px solid #eef0f3; }
.page-title { display: flex; align-items: center; gap: 8px; }
.page-title .title { font-size: 18px; font-weight: 600; }
.id-tag { vertical-align: middle; }
.header-actions { display: inline-flex; gap: 8px; align-items: center; }

.pane-grid {
  height: calc(100vh - 58px);
  display: grid;
  grid-template-columns: minmax(520px, 0.9fr) minmax(520px, 1.1fr);
  gap: 12px;
  padding: 12px 12px 16px;
  overflow: hidden;
}
.pane-card { height: 100%; display: flex; flex-direction: column; min-height: 0; border-radius: 12px; }
.pane-card :deep(.el-card__body) { display: flex; flex-direction: column; flex: 1; min-height: 0; overflow: hidden; padding: 10px; }
.elev { box-shadow: 0 6px 18px rgba(0,0,0,0.05); background: #fff; }
.left-pane, .right-pane { min-height: 0; overflow: hidden; }
.pane-scroll { flex: 1; min-height: 0; overflow: auto; padding-right: 6px; }

/* 吸顶子标题 */
.sticky-subtitle {
  position: sticky; top: 0; z-index: 2;
  background: #fff; padding: 10px 8px; margin: -6px -6px 8px;
  border-bottom: 1px solid #f0f2f5;
  border-top-left-radius: 12px; border-top-right-radius: 12px;
}
.subtitle { font-size: 16px; font-weight: 700; letter-spacing: .2px; }
.right-header { display: flex; align-items: center; justify-content: space-between; }
.right-tools { display: inline-flex; gap: 10px; align-items: center; }

/* 表单样式 */
.form-compact :deep(.el-form-item) { margin-bottom: 12px; }
.block-alert { margin: 4px 0 10px; }
.slider { width: 70%; display: inline-block; }
.slider-val { margin-left: 10px; width: 56px; display: inline-block; text-align: right; font-variant-numeric: tabular-nums; }
.slider-hint { margin-left: 6px; color: #9aa4af; }

/* 提示词卡片 */
.prompt-card {
  border: 1px solid #eef0f3;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
  background: #fafbfd;
  transition: border-color .2s ease, box-shadow .2s ease;
}
.prompt-card:hover { border-color: #e2e6eb; box-shadow: 0 6px 16px rgba(0,0,0,0.04); }
.prompt-card__title { display: flex; align-items: center; justify-content: space-between; gap: 12px; margin-bottom: 8px; }
.prompt-name { font-weight: 600; }
.prompt-input { width: 100%; }
.prompt-actions { margin-top: 8px; display: flex; gap: 8px; }

.ml8 { margin-left: 8px; }
.empty-prompts { padding: 24px 0; }

/* 响应式 */
@media (max-width: 1280px) {
  .pane-grid { grid-template-columns: 1fr; height: calc(100vh - 58px); }
}
</style>
