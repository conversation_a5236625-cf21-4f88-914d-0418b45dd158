<template>
  <el-card :body-style="{ height: 'calc(100% - 80px)', background: '#F5F7FA' }">
    <template #header>
      <div class="card-header">
        <span>题目</span>
        <el-tag type="success" style="margin-left: 10px">{{ areas.length }}个区域</el-tag>
        <el-tag type="warning" style="margin-left: 10px">{{ qsCount }}个题目</el-tag>
        <div class="right-actions"/>
        <el-button text @click="closeExpand" style="color: #999999">折叠所有</el-button>
        <!--        <el-button class="primary-button" style="margin-left: 0;"-->
        <!--                   @click="addAnswerCardArea">-->
        <!--          <el-text class="text">添加答题卡</el-text>-->
        <!--        </el-button>-->
        <el-button class="primary-button" style="margin-left: 10px;" @click="addArea(null)">
          <el-text class="text">添加区域</el-text>
        </el-button>
      </div>
    </template>
    <el-scrollbar class="card-content" height="100%">
      <el-collapse v-model="expandAreas">

        <VueDraggableNext v-model="areas" handle=".handle" @end="setData">
          <transition-group>
            <el-collapse-item v-for="(area, areaIdx) in areas" :key="areaIdx" :name="areaIdx" class="qs-card"
                              style="margin-bottom: 10px;">
              <template #title>
                <div class="qs-card-header">
                  <el-tooltip
                      content="对于题目数量特殊要求的题型，请设置为'通用'题型"
                      placement="top"
                      effect="dark"
                      :show-after="200"
                      :hide-after="50"
                  >
                    <div style="display: flex;align-items: center">
                    <span style="display: flex;flex-direction: row;align-items: center;padding: 0 5px">
                      <el-icon class="handle"><operation/></el-icon>
                      <div style="flex-shrink: 0;width: 45px">区域{{ areaIdx + 1 }}</div>
                    </span>
                    <!-- 答题区域 -->
                    <el-button v-if="cropOptions.type === 'area' && cropOptions.areaIdx === areaIdx"
                               type="warning" icon="Crop" class="area-selecting-button"
                               @click.stop="crop('area', { areaIdx })"
                               style="height: 27px;border: none">
                      <el-text style="color: #FFFFFF;font-size: 13px;opacity: 0.95">停止选取</el-text>
                    </el-button>
                    <el-button v-else-if="area.area && area.area.x && area.area.y"
                               class="right-actions area-selected-button"
                               type="success" icon="Crop"
                               @click.stop="crop('area', { area: area.area, areaIdx })"
                               style="height: 27px;border: none">
                      <el-text style="color: #FFFFFF;font-size: 13px;opacity: 0.95">选取区域</el-text>
                      <el-icon style="margin-left: 5px">
                        <Check/>
                      </el-icon>
                    </el-button>
                    <el-button v-else class="right-actions area-unselected-button" type="primary" icon="Crop"
                               @click.stop="crop('area', { areaIdx })"
                               style="height: 27px;border: none">
                      <el-text style="color: #FFFFFF;font-size: 13px;opacity: 0.95">选取区域</el-text>
                    </el-button>


                    <el-tooltip placement="top" content="识别分数类型 请勿 题目识别">
                      <el-button v-if="area.area != null && area.areaType !== 4" text
                                 @click.stop="extraQs(area, areaIdx)"
                                 :class="getQsOcrButtonClass(area, areaIdx)">
                        <el-image src="/icon/ai.svg" class="icon"></el-image>
                        <el-text class="text">题目识别</el-text>
                      </el-button>
                    </el-tooltip>

                    <div style="margin-left: 10px; display: flex; align-items: center;">
                      <custom-select
                          v-model="area.areaType"
                          @change="changeAreaType(area)"
                          style="width: 160px;"
                          :options="areaTypeOptions"
                          placeholder="选择区域类型"
                      />
                    </div>

                    <el-tag type="danger"
                            style="margin-left: 20px; box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);border-color: #409eff;"
                            @click.stop="showChangeQuestionScoreMessage(areaIdx)">
                      {{ getAreaTotalScore(area) }}分
                    </el-tag>
                    <el-tag type="danger"
                            @click.stop="$refs.scoreTypeDialog.show(areaIdx, scoreTypes, getAreaTotalType(area))"
                            style="margin-left: 10px;box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);border-color: #409eff;">
                      {{ getAreaTotalType(area) }}
                    </el-tag>
                    <el-popover
                        ref="typePicker"
                        trigger="click"
                        placement="bottom-start"
                        width="200"
                    >
                      <el-select
                          v-model="area.commonQuestionType"
                          placeholder="请选择题型"
                          size="small"
                          filterable
                          style="width: 100%;"
                      >
                        <el-option
                            v-for="(type, idx) in questionTypes"
                            :key="idx"
                            :label="`${idx + 1}. ${type}`"
                            :value="type"
                        />
                      </el-select>

                      <template #reference>
                        <el-tag
                            type="danger"
                            style="margin-left: 10px;box-shadow: 0 0 0 2px rgba(64,158,255,0.3);border-color: #409eff;cursor: pointer;"
                            @click.stop
                        >
                          {{ area.commonQuestionType || '选择题型' }}
                        </el-tag>
                      </template>
                    </el-popover>
                  </div>

                  <div style="display: flex;align-items: center">
                    <el-button text icon="Plus" @click.stop="addQuestion(area, areaIdx)"
                    >题目
                    </el-button>
                    <div style="height: 18px;width: 1px;border-left: thin solid #4d92ff;margin: 0 10px"></div>
                    <el-button text @click.stop="deleteArea(areaIdx)"
                    >删除
                    </el-button>
                  </div>
                  </el-tooltip>
                </div>
              </template>

              <div v-if="area.areaType === 2" class="qs-card" style="padding: 0 20px;margin-top: 20px">
                <answer-card-area :ref="`area${areaIdx}`" :index="areaIdx" :value="area"
                                  @input:value="(value) => area = value" :crop-options="cropOptions" @crop="crop"
                                  @copyArea="copyArea"
                                  @deleteArea="deleteArea" :score-types="scoreTypes" :isScore="isScore"/>
              </div>

              <div v-else-if="area.areaType === 1 || area.areaType === 3 || area.areaType === 4" class="qs-card"
                   style="padding: 0 20px;margin-top: 20px">
                <el-collapse v-model="area.expand">
                  <VueDraggableNext v-model="area.questions" handle=".handleCollapse" @end="setData">
                    <transition-group>
                      <el-collapse-item v-for="(question, qsIdx) in area.questions" :key="qsIdx" :name="qsIdx">
                        <template #title>
                          <div style="display: flex;align-items: center;width: 100%;gap: 15px">
                            <span style="display: flex;flex-direction: row;align-items: center;flex-shrink: 0">
                              <el-icon class="handleCollapse"><operation/></el-icon>
                              <div style="margin-left: 5px">{{ `问题${qsIdx + 1}` }}</div>
                            </span>
                            <el-tag v-if="question.scoreType" style="flex-shrink: 0">{{ question.scoreType }}</el-tag>
                            <!-- <el-tag style="margin-left: 10px;">{{ question.opinion === 1 ? '客观' : '主观' }}</el-tag> -->
                            <!-- <el-tag style="margin-left: 10px;">{{ question.reviewType === 1 ? 'AI评价' : '默认评价' }}</el-tag> -->
                            <!-- 答案预览标签 -->
                            <el-tag v-if="question.answer && question.answer.trim()" style="flex-shrink: 0">
                              {{ `答案: ${getAnswerPreview(question.answer)}` }}
                            </el-tag>
                            <el-tag v-show="isScore" style="flex-shrink: 0">{{ `分数: ${question.score}` }}</el-tag>
                            <!-- <el-tag style="margin-left: 10px;" :type="question.reviewArea ?? 'warning'">评价区域</el-tag> -->
                            <el-tag v-if="question.flagArea" style="flex-shrink: 0">勾和叉的区域</el-tag>
                            <div style="flex: 1"></div>
                            <div style="display: flex;align-items: center;gap: 10px;flex-shrink: 0">
                              <el-button text icon="CopyDocument" @click.stop="copyQs(areaIdx, qsIdx)"></el-button>
                              <el-button text icon="Delete" @click.stop="deleteQuestion(areaIdx, qsIdx)"></el-button>
                            </div>
                          </div>
                        </template>
                        <el-form class="qs-form" :model="question" label-position="top" v-if="area.areaType !== 4">
                          <el-form-item label="题目：" prop="question">
                            <el-input v-model="question.question" type="textarea"/>
                          </el-form-item>
                          <el-form-item label="题目信息：" prop="qsInfo">
                            <el-input v-model="question.qsInfo" type="textarea"/>
                          </el-form-item>
                          <!-- 答案格式提示 -->
                          <div v-if="shouldShowAnswerHint(area.commonQuestionType)" class="answer-hint-container">
                            <div class="answer-hint-text">
                              {{ getAnswerHintText(area.commonQuestionType) }}
                            </div>
                            <div class="answer-options">
                              <span
                                v-for="option in getAnswerOptions(area.commonQuestionType)"
                                :key="option"
                                class="answer-option"
                                @click="copyAnswerOption(option)"
                                :title="'点击即可复制'"
                              >
                                {{ option }}
                              </span>
                            </div>
                          </div>
                          <el-form-item label="答案：" prop="answer">
                            <el-input v-model="question.answer" type="textarea"/>
                          </el-form-item>
                          <el-form-item label="分数：" prop="score">
                            <el-space>
                              <el-input-number v-model="question.score" :controls="false" style="width: 200px"/>
                              <el-tooltip
                                  effect="dark"
                                  content="同步分数到区域"
                                  placement="top"
                              >
                                <el-button text @click="syncAreaScore(areaIdx, qsIdx)" icon="RefreshRight"></el-button>
                              </el-tooltip>
                            </el-space>
                          </el-form-item>
                          <!-- <el-form-item prop="isAdditional" label="附加题">
                          <el-switch v-model="question.isAdditional" :active-value="2" :inactive-value="1" />
                      </el-form-item> -->
                          <el-form-item prop="scoreType" label="分数类型">
                            <custom-select
                                v-model="question.scoreType"
                                style="width: 200px"
                                :disabled="question.isAdditional === 2"
                                placeholder="请选择分数类型"
                                :options="questionScoreTypeOptions"
                            />
                            <el-tooltip
                                effect="dark"
                                content="同步类型到区域"
                                placement="top"
                            >
                              <el-button text @click="syncAreaType(areaIdx, qsIdx)" icon="RefreshRight"
                                         style="margin-left: 8px"></el-button>
                            </el-tooltip>
                          </el-form-item>
                          <div style="display: flex;gap: 24px;margin-bottom: 10px">
                            <el-text>客观</el-text>
                            <el-switch v-model="question.opinion" :active-value="1" :inactive-value="2"/>
                            <el-text>得分点题目</el-text>
                            <el-switch v-model="question.isScorePoint" :active-value="2" :inactive-value="1"/>
                            <el-switch v-model="question.reviewType" :active-value="2" :inactive-value="1"
                                       active-text="默认评价" inactive-text="AI评价"/>

                            <el-switch v-model="question.dontShowText" :active-value="true" :inactive-value="false"
                                       inactive-text="不显示答案"/>
                          </div>

                          <el-form-item v-show="question.isScorePoint === 2" label="得分点">
                            <el-input v-model="question.scorePoints" type="textarea"
                                      :autosize="{ minRows: 3 }"/>
                          </el-form-item>

                          <el-form-item v-show="question.reviewType === 2" label="默认评价">
                            <el-input v-model="question.defaultReview" type="textarea"
                                      :autosize="{ minRows: 3 }"/>
                          </el-form-item>
                          <el-form-item>
                            <!-- 评价区域 -->
                            <div v-if="question.defaultReview" style="margin-right: 10px">
                              <el-button
                                  v-if="cropOptions.type === 'review' && cropOptions.areaIdx === areaIdx && cropOptions.qsIdx === qsIdx"
                                  type="warning" icon="Crop"
                                  @click="crop('review', { areaIdx, qsIdx })">评价区域
                              </el-button>
                              <el-button v-else-if="question.reviewArea" type="success" icon="Crop"
                                         @click="crop('review', { area: question.reviewArea, areaIdx, qsIdx })">评价区域
                              </el-button>
                              <el-button v-else type="primary" icon="Crop"
                                         @click="crop('review', { areaIdx, qsIdx })">评价区域
                              </el-button>
                            </div>

                            <!-- 勾和叉的区域 -->
                            <el-button
                                v-if="cropOptions.type === 'flag' && cropOptions.areaIdx === areaIdx && cropOptions.qsIdx === qsIdx"
                                type="warning" icon="Crop"
                                @click="crop('flag', { areaIdx, qsIdx })"
                                style="height: 27px;background: #eebe77;border: none">
                              <el-text style="color: #FFFFFF;font-size: 13px;opacity: 0.95">停止选取</el-text>
                            </el-button>
                            <el-button v-else-if="question.flagArea && question.flagArea.x && question.flagArea.y"
                                       type="success" icon="Crop"
                                       @click="crop('flag', { area: question.flagArea, areaIdx, qsIdx })"
                                       style="height: 27px;background: #15c29d;border: none">
                              <el-text style="color: #FFFFFF;font-size: 13px;opacity: 0.95">勾和叉的位置</el-text>
                              <el-icon style="margin-left: 5px">
                                <Check/>
                              </el-icon>
                            </el-button>
                            <el-button v-else type="primary" icon="Crop"
                                       @click="crop('flag', { areaIdx, qsIdx })"
                                       style="height: 27px;background: #1677ff;border: none">
                              <el-text style="color: #FFFFFF;font-size: 13px;opacity: 0.95">勾和叉的位置</el-text>
                            </el-button>

                            <!-- 题目标记 -->
                            <!--                          <el-button-->
                            <!--                              v-if="cropOptions.type === 'qsPos' && cropOptions.areaIdx === areaIdx && cropOptions.qsIdx === qsIdx"-->
                            <!--                              type="warning" icon="Crop"-->
                            <!--                              @click="crop('qsPos', { areaIdx, qsIdx })">题目位置-->
                            <!--                          </el-button>-->
                            <!--                          <el-button v-else-if="question.qsPosArea" type="success" icon="Crop"-->
                            <!--                                     @click="crop('qsPos', { area: question.qsPosArea, areaIdx, qsIdx })">题目位置-->
                            <!--                          </el-button>-->
                            <!--                          <el-button v-else type="primary" icon="Crop"-->
                            <!--                                     @click="crop('qsPos', { areaIdx, qsIdx })">题目位置-->
                            <!--                          </el-button>-->

                            <!--                          <el-button type="plain" icon="Delete"-->
                            <!--                                     @click="deleteQuestion(areaIdx, qsIdx)">删除-->
                            <!--                          </el-button>-->
                          </el-form-item>
                        </el-form>

                        <el-form class="qs-form" :model="question" label-position="top" v-else>
                          <el-form-item v-show="isScore" label="分数：" prop="score">
                            <el-space>
                              <el-input-number v-model="question.score" :controls="false" style="width: 200px"/>
                              <el-button type="primary" @click="syncAreaScore(areaIdx, qsIdx)">同步分数到区域
                              </el-button>
                            </el-space>
                          </el-form-item>

                          <el-form-item prop="scoreType" label="分数类型">
                            <el-select v-model="question.scoreType" style="width: 200px"
                                       :disabled="question.isAdditional === 2" placeholder="请选择分数类型">
                              <el-option v-for="item in scoreTypes" :key="item" :label="item" :value="item"/>
                            </el-select>
                            <el-button type="primary" @click="syncAreaType(areaIdx, qsIdx)" style="margin-left: 10px">
                              同步类型到区域
                            </el-button>
                          </el-form-item>
                          <el-form-item>
                            <el-text>得分点题目</el-text>
                            <el-switch v-model="question.isScorePoint" :active-value="2" :inactive-value="1"/>
                          </el-form-item>

                          <!-- 勾和叉的区域 -->
                          <el-button
                              v-if="cropOptions.type === 'flag' && cropOptions.areaIdx === areaIdx && cropOptions.qsIdx === qsIdx"
                              type="warning" icon="Crop"
                              @click="crop('flag', { areaIdx, qsIdx })">勾和叉的位置
                          </el-button>
                          <el-button v-else-if="question.flagArea" type="success" icon="Crop"
                                     @click="crop('flag', { area: question.flagArea, areaIdx, qsIdx })">勾和叉的位置
                          </el-button>
                          <el-button v-else type="primary" icon="Crop"
                                     @click="crop('flag', { areaIdx, qsIdx })">勾和叉的位置
                          </el-button>
                        </el-form>
                      </el-collapse-item>
                    </transition-group>
                  </VueDraggableNext>
                </el-collapse>

              </div>
            </el-collapse-item>

          </transition-group>
        </VueDraggableNext>
      </el-collapse>

      <el-collapse v-if="config.remarkArea" v-model="remarkExpand" class="qs-card">
        <el-collapse-item name="remark">
          <template #title>
            <div class="qs-card-header">
              <div style="display:flex;align-items:center">
          <span style="display:flex;align-items:center;padding:0 5px">
            <el-icon class="handle"><operation/></el-icon>
            <div style="flex-shrink:0;margin-left:7px">评语</div>
          </span>

                <!-- 评语裁剪三态按钮 -->
                <el-button
                    v-if="cropOptions.type === 'remark'"
                    type="warning" icon="Crop"
                    @click.stop="crop('remark', {})"
                    style="height:27px;border:none;margin-left:10px">
                  <el-text style="color:#fff;font-size:13px;opacity:.95">停止选取</el-text>
                </el-button>
                <el-button
                    v-else-if="config.remarkArea && config.remarkArea.x != null && config.remarkArea.y != null"
                    type="success" icon="Crop"
                    @click.stop="crop('remark', { area: config.remarkArea })"
                    style="height:27px;border:none;margin-left:10px">
                  <el-text style="color:#fff;font-size:13px;opacity:.95">选取评语区域</el-text>
                  <el-icon style="margin-left:5px"><Check/></el-icon>
                </el-button>
                <el-button
                    v-else
                    type="primary" icon="Crop"
                    @click.stop="crop('remark', {})"
                    style="height:27px;border:none;margin-left:10px">
                  <el-text style="color:#fff;font-size:13px;opacity:.95">选取评语区域</el-text>
                </el-button>

                <!-- 标题行展示“随机/固定”状态 -->
                <el-tag
                    class="remark-mode-tag"
                    :type="remarkModeTag.type"
                    effect="light"
                    round
                    style="margin-left:12px">
                  {{ remarkModeTag.text }}
                </el-tag>
              </div>

              <div style="display:flex;align-items:center">
                <!-- 可选：右侧操作位 -->
              </div>
            </div>
          </template>

          <!-- 卡片内容（更紧凑的表单排版） -->
          <div class="qs-form" style="padding: 20px">
            <el-form
                class="remark-form"
                :model="config"
                label-position="left"
                label-width="70px"
            >

              <el-form-item label="来源">
                <el-radio-group v-model="config.isRandomRemark" >
                  <el-radio-button :value="true">随机值</el-radio-button>
                  <el-radio-button :value="false">固定值</el-radio-button>
                </el-radio-group>
                <el-text type="info" style="margin-left:10px" v-if="config.isRandomRemark === true">
                  系统将从评语库中随机抽取
                </el-text>
              </el-form-item>

              <el-form-item v-if="config.isRandomRemark === false" label="固定内容">
                <el-space wrap>
                  <el-select
                      v-model="config.remarkContent"
                      filterable
                      allow-create
                      default-first-option
                      clearable
                      remote
                      :remote-method="loadRemarkOptions"
                      :loading="remarkLoading"
                      placeholder="请选择或输入评语内容"
                      style="width:420px">
                    <el-option v-for="opt in remarkOptions" :key="opt" :label="opt" :value="opt" />
                  </el-select>
                  <el-button text icon="Refresh" @click="loadRemarkOptions('')">刷新</el-button>
                  <el-text type="info" v-if="!config.remarkContent">未选择固定内容</el-text>
                </el-space>
              </el-form-item>

              <el-form-item label="选区">
                <el-space wrap>
                  <!-- 复用标题处的三态按钮逻辑，保留在标题即可，这里只做辅助信息 -->
                  <el-tag size="small" class="coords">x: {{ config.remarkArea?.x ?? '-' }}</el-tag>
                  <el-tag size="small" class="coords">y: {{ config.remarkArea?.y ?? '-' }}</el-tag>
                  <el-tag size="small" class="coords">w: {{ config.remarkArea?.width ?? '-' }}</el-tag>
                  <el-tag size="small" class="coords">h: {{ config.remarkArea?.height ?? '-' }}</el-tag>
                </el-space>
              </el-form-item>

            </el-form>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- ✅ 扣分区域（可多个） -->
      <el-collapse
          v-if="(config.deductionAreas || []).length"
          v-model="deductionExpand"
          class="qs-card"
          style="margin-top: 16px"
      >
        <el-collapse-item
            v-for="(d, dIdx) in config.deductionAreas"
            :key="dIdx"
            :name="String(dIdx)"
        >
          <template #title>
            <div class="qs-card-header">
              <div style="display:flex;align-items:center">
              <span style="display:flex;align-items:center;padding:0 5px">
                <el-icon class="handle"><operation/></el-icon>
                <div style="flex-shrink:0;margin-left:7px">扣分区域 {{ dIdx + 1 }}</div>
              </span>

                <!-- 裁剪三态 -->
                <el-button
                    v-if="cropOptions.type === 'deduction-area' && cropOptions.qsIdx === dIdx"
                    type="warning" icon="Crop"
                    @click.stop="crop('deduction-area', { areaIdx: null, qsIdx: dIdx })"
                    style="height:27px;border:none;margin-left:10px">
                  <el-text style="color:#fff;font-size:13px;opacity:.95">停止选取</el-text>
                </el-button>
                <el-button
                    v-else-if="d.area && d.area.x != null && d.area.y != null"
                    type="success" icon="Crop"
                    @click.stop="crop('deduction-area', { area: d.area, areaIdx: null, qsIdx: dIdx })"
                    style="height:27px;border:none;margin-left:10px">
                  <el-text style="color:#fff;font-size:13px;opacity:.95">选取扣分区域</el-text>
                  <el-icon style="margin-left:5px"><Check/></el-icon>
                </el-button>
                <el-button
                    v-else
                    type="primary" icon="Crop"
                    @click.stop="crop('deduction-area', { areaIdx: null, qsIdx: dIdx })"
                    style="height:27px;border:none;margin-left:10px">
                  <el-text style="color:#fff;font-size:13px;opacity:.95">选取扣分区域</el-text>
                </el-button>

                <el-tag
                    v-if="(d.areaIdxs || []).length"
                    type="danger"
                    effect="light"
                    round
                    style="margin-left:12px"
                >
                  已关联 {{ d.areaIdxs.length }} 个区域
                </el-tag>
              </div>

              <div style="display:flex;align-items:center">
                <el-button text type="danger" icon="Delete" @click.stop="removeDeductionArea(dIdx)">删除</el-button>
              </div>
            </div>
          </template>

          <!-- 卡片内容 -->
          <div class="qs-form" style="padding: 20px">
            <el-form :model="d" label-position="left" label-width="86px" class="deduction-form">
              <el-form-item label="关联区域">
                <el-select
                    v-model="d.areaIdxs"
                    multiple
                    filterable
                    collapse-tags
                    collapse-tags-tooltip
                    clearable
                    placeholder="选择需要应用扣分的区域"
                    style="width: 480px"
                >
                  <el-option
                      v-for="opt in areaSelectOptions"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                  />
                </el-select>
                <el-text type="info" style="margin-left:10px">
                  可多选
                </el-text>
              </el-form-item>

              <el-form-item label="坐标">
                <el-space wrap>
                  <el-tag size="small" class="coords">x: {{ d.area?.x ?? '-' }}</el-tag>
                  <el-tag size="small" class="coords">y: {{ d.area?.y ?? '-' }}</el-tag>
                  <el-tag size="small" class="coords">w: {{ d.area?.width ?? '-' }}</el-tag>
                  <el-tag size="small" class="coords">h: {{ d.area?.height ?? '-' }}</el-tag>
                </el-space>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-item>
      </el-collapse>


    </el-scrollbar>
    <ScoreTypeDialog ref="scoreTypeDialog" @submit="syncAreaTypeByAreaType"></ScoreTypeDialog>
  </el-card>
</template>
<script>
import {extraImg} from '../../../utils/imgUtil'
import AnswerCardArea from './answerCardArea.vue'
import {VueDraggableNext} from 'vue-draggable-next'
import {Operation} from '@element-plus/icons-vue';
import {ElMessageBox, ElNotification} from "element-plus";
import {useUserStore} from "@/store";
import {ref, h} from 'vue';
import {autoSortAreas, isSameOrder} from '@/utils/sortAreas'
import ScoreTypeDialog from './ScoreTypeDialog.vue'
import CustomSelect from './CustomSelect.vue'

const store = useUserStore();

export default {
  components: {
    AnswerCardArea,
    VueDraggableNext,
    Operation,
    ScoreTypeDialog,
    CustomSelect
  },
  props: {
    cropOptions: {
      type: Object,
      required: true,
    },
    isScore: {
      type: Boolean,
      required: true,
    },
    qsOcrPrompt: {
      type: String,
      default: null,
    },
    scoreTypes: {
      type: Array,
      default: () => [],
    },
    config: {
      type: Object,
      default: () => {
      }
    },
    extractingAreas: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      questionTypes: [
        '通用',
        '单选题',
        '多选题',
        '判断题',
        '填空题',
        '简单的四则运算',
        '数学计算题',
        '数学应用题',
        '连线题',
        '画图题',
        '翻译题',
        '图表题',
        '涂卡题',
        '涂卡选择题',
        '涂卡判断题'
      ],
      areaTypeOptions: [
        {value: 4, label: '识别分数', icon: 'Tools'},
        {value: 1, label: '普通题目', icon: 'Document'},
        {value: 2, label: '答题卡区域', icon: 'Grid'},
        {value: 3, label: '写作题', icon: 'Edit'},
        {value: 5, label: '评语', icon: 'ChatLineSquare'},
        { value: 6, label: '扣分区域', icon: 'TakeawayBox' }
      ],
      expandAreas: [],
      areas: [],
      opinionOptions: [
        {
          value: 1,
          label: "客观",
        },
        {
          value: 2,
          label: "主观",
        },
      ],
      areaTemplate: {
        areaType: 1,
        commonQuestionType: '通用',
        area: {
          x: 0,
          y: 0,
          width: 50,
          height: 50,
          rotate: 0,
          scaleX: 1,
          scaleY: 1
        },
        enabled: true,
        opinion: 1,
        questions: [
          {
            name: "问题1",
            question: "",
            qsInfo: "",
            answer: "",
            score: 1,
            isAdditional: 1,
            flagArea: {
              x: 0,
              y: 0,
              width: 50,
              height: 50,
              rotate: 0,
              scaleX: 1,
              scaleY: 1
            },
            reviewType: 2,
            defaultReview: '',
            scoreType: '总分',
            opinion: 2,
            isScorePoint: 1,
            scorePoints: "",
            dontShowText: false
          },
        ],
        expand: [],
      },

      areaTemplateByAreaType2: {
        areaType: 2,
        commonQuestionType: '通用',
        area: {
          x: 0,
          y: 0,
          width: 50,
          height: 50,
          rotate: 0,
          scaleX: 1,
          scaleY: 1
        },
        enabled: true,
        questions: [
          {
            score: 1,
            scoreType: '总分',
            optionArea: null, // 选项区域坐标（需用户选择）
            answer: [],       // 正确答案数组（如 [0,2] 表示选中第1和第3个选项）
            choiceMode: 0,    // 选择模式：0-横向，1-纵向
            choiceNum: 1      // 选项数量（如4表示ABCD）
          }
        ],
        expand: []
      },
      // 识别分数类型模板
      areaTemplateByAreaType4: {
        areaType: 4,
        commonQuestionType: '通用',
        area: {
          x: 0,
          y: 0,
          width: 50,
          height: 50,
          rotate: 0,
          scaleX: 1,
          scaleY: 1
        },
        enabled: true,
        opinion: 1,
        questions: [
          {
            name: "问题1",
            question: "识别老师批改的分数",
            qsInfo: "识别老师批改的分数",
            answer: "无",
            score: 1,
            isAdditional: 1,
            flagArea: {
              x: 0,
              y: 0,
              width: 50,
              height: 50,
              rotate: 0,
              scaleX: 1,
              scaleY: 1
            },
            reviewType: 2,
            defaultReview: '',
            scoreType: '总分',
            opinion: 2,
            isScorePoint: 2,
            scorePoints: "",
            dontShowText: false
          },
        ],


      },
      remarkOptions: [],
      remarkLoading: false,

      expand: [],
      remarkExpand: ['remark'],
      deductionExpand: [],
    }
  },
  created() {
    this.areas = [JSON.parse(JSON.stringify(this.areaTemplate))]

    this.$axios.get('/api/prompts/questionTypes').then(res => {
      console.log(res)
      this.questionTypes = res.data;
    })

    if (this.config.isRandomRemark === undefined) this.config.isRandomRemark = true;
    if (!this.config.hasOwnProperty('remarkContent')) this.config.remarkContent = '';
    // 不主动创建 remarkArea；由“改为评语”动作来生成
    this.loadRemarkOptions('');

    if (!Array.isArray(this.config.deductionAreas)) this.config.deductionAreas = [];

  },
  watch: {
    expandAreas: {
      handler(newVal, oldVal) {
        // 新展开的给一个提示：已展开区域X
        // 旧的折叠的给一个提示：已折叠区域X
        if (newVal.length > oldVal.length) {
          let areaIdx = newVal.filter(e => !oldVal.includes(e))[0]
          this.$message.success(`已展开区域${areaIdx + 1}`)
        } else {
          let areaIdx = oldVal.filter(e => !newVal.includes(e))[0]
          this.$message.success(`已折叠区域${areaIdx + 1}`)
        }
        this.$emit('update:areas', newVal)
      },
      deep: true
    },
    areas: {
      handler(newVal, oldVal) {
        // 判断，区域是否改变
        if (newVal && store.getIsAutoSortArea) {
          const newOrder = autoSortAreas(newVal);
          if (!isSameOrder(newVal, newOrder)) {
            ElMessageBox.alert('检测到区域并没有按照从上到下从左右到的排序排序，是否调整？', '区域顺序调整', {
              confirmButtonText: '调整',
              cancelButtonText: '取消',
              type: 'warning',
            }).then((msg) => {
              if (msg === 'confirm') {
                this.areas = newOrder;
              }
            });

          } else {
          }
        }
        this.$emit('refreshCropper', newVal)
      },
      deep: true
    }
  },
  computed: {
    areaSelectOptions() {
      // 生成：[{label:'区域1', value:0}, ...]
      return (this.areas || []).map((_, idx) => ({
        label: `区域${idx + 1}`,
        value: idx
      }));
    },

    remarkModeTag() {
      const isFixed = this.config?.isRandomRemark === false;
      const preview = (this.config?.remarkContent || '').trim();
      return {
        type: isFixed ? 'warning' : 'success',
        text: isFixed ? `固定值：${preview || '未选择'}` : '随机值'
      };
    },

    questionScoreTypeOptions() {
      // 5种循环使用的图标
      const scoreTypeIcons = ['Trophy', 'Star', 'Medal', 'Tools', 'Flag'];

      return this.scoreTypes.map((type, index) => ({
        value: type,
        label: type,
        icon: scoreTypeIcons[index % scoreTypeIcons.length]
      }));
    },
    qsCount() {
      let count = 0
      this.areas.forEach(area => {
        count += area.questions.length
      })
      return count
    },
    scoreTypesAndTotalScore() {
      let typesAndScores = {};
      this.areas.forEach(area => {
        area.questions.forEach(qs => {
          if (qs.scoreType !== '总分') {
            if (!typesAndScores[qs.scoreType]) {
              typesAndScores[qs.scoreType] = qs.score;
            } else {
              typesAndScores[qs.scoreType] += qs.score;
            }
          }
        });
      });
      let res = []
      for (let key in typesAndScores) {
        res.push({
          type: key,
          score: typesAndScores[key]
        })
      }
      return res;
    },
    totalScore() {
      let total = 0
      this.areas.forEach(area => {
        area.questions.forEach(qs => {
          if (qs.isAdditional === 1) {
            total += qs.score
          }
        })
      })
      return total
    },
    additionalScore() {
      let total = 0
      this.areas.forEach(area => {
        area.questions.forEach(qs => {
          if (qs.isAdditional === 2) {
            total += qs.score
          }
        })
      })
      return total
    },
  },
  methods: {
    addDeductionArea() {
      if (!Array.isArray(this.config.deductionAreas)) this.config.deductionAreas = [];
      this.config.deductionAreas.push({
        area: { x: 0, y: 0, width: 50, height: 50, rotate: 0, scaleX: 1, scaleY: 1 },
        areaIdxs: []
      });
      const name = String(this.config.deductionAreas.length - 1);
      if (!Array.isArray(this.deductionExpand)) this.deductionExpand = [];
      if (!this.deductionExpand.includes(name)) this.deductionExpand.push(name);
    },

    removeDeductionArea(dIdx) {
      this.config.deductionAreas.splice(dIdx, 1);
      // 维护展开状态
      this.deductionExpand = (this.deductionExpand || [])
          .filter(n => Number(n) !== dIdx)
          .map(n => String(Number(n) > dIdx ? Number(n) - 1 : Number(n)));
      this.$message.success('已删除扣分区域');
    },

    loadRemarkOptions(keyword = '') {
      this.remarkLoading = true;
      this.$axios.get('/api/remark/page', {
        params: {pageNumber: 1, pageSize: 50, keyword}
      }).then(res => {
        const data = res.data || {};
        const records = data.records || data.list || data.items || [];
        this.remarkOptions = (records || []).map(r => r.content).filter(Boolean);
      }).finally(() => {
        this.remarkLoading = false;
      });
    },

    getAnswerPreview(answer) {
      // 如果答案为空或只有空白字符，返回空字符串
      if (!answer || !answer.trim()) {
        return '';
      }
      // 截取前8个字符，如果超过8个字符则添加省略号
      const trimmedAnswer = answer.trim();
      if (trimmedAnswer.length > 8) {
        return trimmedAnswer.substring(0, 8) + '...';
      }
      return trimmedAnswer;
    },
    getQsOcrButtonClass(area, areaIdx) {
      // 如果正在识别中，显示灰色
      if (this.extractingAreas.includes(areaIdx)) {
        return 'qs-ocr-pending';
      }
      // 如果已经有识别结果，显示绿色
      if (area.questions && area.questions.length > 0) {
        return 'qs-ocr-completed';
      }
      // 默认待识别状态，显示灰色
      return 'qs-ocr-pending';
    },
    showChangeQuestionScoreMessage(areaIdx) {
      this.$prompt('请输入每个题目的分数', '同步修改每小题分数', {
        inputValue: '',
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(async ({value}) => {
        let score = Number.parseFloat(value);
        this.syncAreaScoreByScore(areaIdx, score)
      }).catch((e) => {
        // 用户点击取消时的处理逻辑
        this.$message.info("已取消修改");
      });
    },
    validateAreas() {
      this.areas.forEach(area => {
        if (area.areaType === 2) {
          const question = area.questions[0]
          if (!question.optionArea) {
            throw new Error('答题卡区域必须选择选项区域!')
          }
          if (question.answer.length === 0) {
            throw new Error('请设置正确答案!')
          }
        }
      })
    },
    getAreaTotalScore(area) {
      let total = 0
      area.questions.forEach(qs => {
        total += qs.score
      })
      return total
    },
    getAreaTotalType(area) {
      let total = [];
      area.questions.forEach(qs => {
        if (!total.includes(qs.scoreType)) {
          total.push(qs.scoreType)
        }
      })
      // 合并为字符串
      return total.join(",")
    },
    changeAreaType(area) {
      if (area.areaType === 5 && this.config.remarkArea) {
        this.$message.warning('已存在评语区域，不能再次设置为评语');
        return;
      }
      if (area.areaType === 2) {
        if (area.questions.length > 1) {
          ElMessageBox.confirm('答题卡区域只能有一个题目，是否删除多余题目？')
              .then(() => {
                this.$message.success('已删除多余题目!')
                area.questions = area.questions.slice(0, 1)
                // 初始化答题卡题目结构
                area.questions[0] = {
                  score: 1,
                  scoreType: '总分',
                  optionArea: null,
                  answer: [],
                  choiceMode: 0,
                  choiceNum: 1
                }
              })
              .catch(() => {
                area.areaType = 1 // 回退类型
              })
        } else if (area.questions.length === 1) {
          // 初始化答题卡题目结构
          area.questions[0] = {
            score: 1,
            scoreType: '总分',
            optionArea: null,
            answer: [],
            choiceMode: 0,
            choiceNum: 1
          }
        }
      } else if (area.areaType === 4) {
        if (area.questions.length > 1) {
          ElMessageBox.confirm('识别分数区域只支持一个题目，是否删除多余题目？')
              .then(() => {
                this.$message.success('已删除多余题目!')
                area.questions = area.questions.slice(0, 1)
                area.questions[0].isScorePoint = 2;
                area.questions[0].question = "识别老师批改的分数";
                area.questions[0].qsInfo = "识别老师批改的分数";
                area.questions[0].answer = "无";
              })
              .catch(() => {
                area.areaType = 1
              })
        }
        if (area.questions.length === 1) {
          area.questions[0].isScorePoint = 2;
          area.questions[0].question = "识别老师批改的分数";
          area.questions[0].qsInfo = "识别老师批改的分数";
          area.questions[0].answer = "无";
        }
      } else if (area.areaType === 5) {
        // 计算 remarkArea 初始框：当前区域的“最右侧中间位置”，大小 50x50
        const a = area.area;
        let x = 0, y = 0;
        if (a && typeof a.x === 'number' && typeof a.y === 'number' &&
            typeof a.width === 'number' && typeof a.height === 'number') {
          x = Math.max(0, a.x + a.width - 50);
          y = Math.max(0, a.y + Math.floor(a.height / 2) - 25);
        }
        this.config.remarkArea = {
          x, y, width: 50, height: 50, rotate: 0, scaleX: 1, scaleY: 1
        };

        // 从 areas 删除这个区域
        const idx = this.areas.indexOf(area);
        if (idx !== -1) this.areas.splice(idx, 1);

        this.$message.success('已切换为“评语”并设置默认评语区域');
      } else if (area.areaType === 6) {
        if (!Array.isArray(this.config.deductionAreas)) this.config.deductionAreas = [];

        // 默认坐标：当前区域右侧中点处 50x50（若当前无 area 则 0,0）
        const a = area.area;
        let x = 0, y = 0;
        if (a && typeof a.x === 'number' && typeof a.y === 'number' &&
            typeof a.width === 'number' && typeof a.height === 'number') {
          x = Math.max(0, a.x + a.width - 50);
          y = Math.max(0, a.y + Math.floor(a.height / 2) - 25);
        }

        // 新的扣分区域对象
        this.config.deductionAreas.push({
          area: { x, y, width: 50, height: 50, rotate: 0, scaleX: 1, scaleY: 1 },
          areaIdxs: [] // 先不绑定，交给用户在下拉多选中选择
        });

        // 从 areas 中删除当前区域
        const idx = this.areas.indexOf(area);
        if (idx !== -1) this.areas.splice(idx, 1);

        // 展开最新的扣分折叠
        if (!Array.isArray(this.deductionExpand)) this.deductionExpand = [];
        const newIdx = (this.config.deductionAreas.length - 1).toString();
        if (!this.deductionExpand.includes(newIdx)) this.deductionExpand.push(newIdx);

        this.$message.success('已切换为“扣分区域”，并生成一个独立的扣分配置卡片');
      }

    },
    setData(areas) {
      if (!areas || areas.length === 0) {
        this.areas = JSON.parse(JSON.stringify(this.areaTemplate))
      } else {
        areas.forEach(area => {
          if (!area.areaType) {
            area.areaType = 1 // 默认区域批改
          }
          if (!area.enabled && area.enabled !== false) {
            area.enabled = true
          }
          area.expand = []
          if (!area.opinion) {
            area.opinion = 1
          }
          area.questions.forEach(qs => {
            if (!qs.qsInfo) {
              qs.qsInfo = ""
            }
            if (!qs.reviewType) {
              qs.reviewType = 1
              qs.defaultReview = ''
            }
            if (!qs.isAdditional) {
              qs.isAdditional = 1
            }
            if (!qs.scoreType) {
              qs.scoreType = '总分'
            }
            if (!qs.opinion) {
              qs.opinion = 2
            }
            if (!qs.isScorePoint) {
              qs.isScorePoint = 1
            }
          })
        })
      }
      this.areas = areas
    },
    addArea(areaPos = null, isUseOcr = false, isScorePoint = false) {
      if (!isScorePoint) {
        this.areas.push(JSON.parse(JSON.stringify(this.areaTemplate)))
        this.$message.success('已添加区域')
        if (areaPos) {
          this.areas[this.areas.length - 1].area = areaPos;
          // 自动题目识别
          if (isUseOcr) {
            this.$nextTick(() => {
              this.extraQs(this.areas[this.areas.length - 1], this.areas.length - 1)
            })
          }

        }
      } else {
        this.areas.push(JSON.parse(JSON.stringify(this.areaTemplateByAreaType4)))
        this.$message.success('已添加区域')
        if (areaPos) {
          this.areas[this.areas.length - 1].area = areaPos;
        }
      }


      // this.$confirm('请选择题目类型', '提示', {
      //   confirmButtonText: '普通题目',
      //   cancelButtonText: '分数识别',
      //   type: 'info'
      // }).then(() => {
      //   // 用户选择了普通题目，执行相关逻辑
      //   this.areas.push(JSON.parse(JSON.stringify(this.areaTemplate)))
      //   this.$message.success('已添加区域')
      // }).catch(() => {
      //   // 用户选择了分数识别，执行相应处理
      //   this.areas.push(JSON.parse(JSON.stringify(this.areaTemplateByAreaType4)))
      //   this.$message.success('已添加区域')
      // });
    },
    addQuestion(area, areaIdx) {
      if (area.areaType === 2) {
        this.$message.warning('答题卡区域只能有一个题目!')
        return
      }
      if (area.areaType === 4 && area.questions && area.questions.length > 1) {
        this.$message.warning('识别分数区域只能有一个题目!')
        return
      }
      this.areas[areaIdx].questions.push({
        name: `问题${this.areas[areaIdx].questions.length + 1}`,
        question: "",
        qsInfo: "",
        answer: "",
        score: 1,
        isAdditional: 1,
        flagArea: {
          x: 0,
          y: 0,
          width: 50,
          height: 50,
          rotate: 0,
          scaleX: 1,
          scaleY: 1
        },
        reviewType: 2,
        defaultReview: '',
        scoreType: '总分',
        opinion: 2,
        isScorePoint: 1,
        scorePoints: "",
      })
    },
    deleteArea(areaIdx) {
      if (this.cropOptions.areaIdx === areaIdx) {
        this.$message.error('请取消选择!')
        return
      }
      this.areas.splice(areaIdx, 1)
      this.$message.success('已删除区域')
    },
    deleteQuestion(areaIdx, qsIdx) {
      if (this.cropOptions.areaIdx === areaIdx && this.cropOptions.qsIdx === qsIdx) {
        this.$message.error('请取消选择!')
        return
      }
      this.areas[areaIdx].questions.splice(qsIdx, 1)
    },
    crop(type, { area, areaIdx, qsIdx }) {
      const payload = { area, areaIdx, qsIdx };
      // 仅当属于 areas 的普通区域时带上 areaType
      if (typeof areaIdx === 'number' && this.areas[areaIdx]) {
        payload.areaType = this.areas[areaIdx].areaType;
      }
      this.$emit('crop', type, payload);
    },
    setArea(type, area, areaIdx, qsIdx) {
      console.log('setArea', type, area, areaIdx, qsIdx)
      if (type === 'area') {
        this.areas[areaIdx].area = area
      }
      if (type === 'review') {
        this.areas[areaIdx].questions[qsIdx].reviewArea = area
      }
      if (type === 'flag') {
        this.areas[areaIdx].questions[qsIdx].flagArea = area
      }
      if (type === 'option') {
        this.areas[areaIdx].questions[qsIdx].optionArea = area
      }
      if (type === 'qsPos') {
        this.areas[areaIdx].questions[qsIdx].qsPosArea = area
      }

      if (type === 'remark') {
        this.config.remarkArea = area;
      }
      if (type === 'deduction-area') {
        // 新：当从“扣分折叠卡片”触发裁剪时，不会传 areaIdx，此时用 qsIdx 作为扣分卡片的索引
        if ((areaIdx === undefined || areaIdx === null) && Array.isArray(this.config.deductionAreas)) {
          const dIdx = qsIdx; // 这里用 qsIdx 传递扣分卡片索引
          if (this.config.deductionAreas[dIdx]) {
            this.config.deductionAreas[dIdx].area = area;
          }
        } else if (typeof areaIdx === 'number' && this.areas[areaIdx]) {
          // 兼容旧逻辑（若仍有内部使用）
          this.areas[areaIdx].deductionArea = area;
        }
        return;
      }


      if (this.areas[areaIdx].questions[qsIdx]?.areaType === 2) {
        // 答题卡区域更新
        this.$nextTick(() => {
          this.$refs[`area${areaIdx}`].updateValue()
        })
      }
    },
    startOcr(areaIdx) {
      this.extraQs(this.areas[areaIdx], areaIdx)
    },
    extraQs(area, areaIdx) {
      let areaType = this.areas[areaIdx].areaType;
      if (areaType === 4) {
        return this.$message.warn("题目识别不支持区域识别")
      }
      const img = document.getElementById("img");
      if (img == null) {
        this.$message.warning('请上传图片')
        return
      }
      const imgStr = extraImg(img, area.area);
      const modalOption = store.getAimodelOptions[store.getDefaultCorrectModal]
      console.log('modalOption', modalOption, store.getAimodelOptions)
      console.log('modalOption', modalOption, store.getDefaultCorrectModal)
      let form = {
        imgStr: imgStr.replace("data:image/jpeg;base64,", ""), // 去除base64前缀
        prompt: this.qsOcrPrompt,
        enableOcr: this.config?.qsOCRPlus ?? false,
        ocrService: this.config?.qsOcrService ?? 'default',
        aimodel: modalOption.value,
        jsonobject: modalOption.jsonobject ?? false,
        jsonschema: modalOption.jsonschema ?? false
      }
      const totalSeconds = 10;
      const currentSeconds = ref(0);
      const NotificationContent = {
        setup() {
          // 返回渲染函数，根据 currentSeconds 的值生成文本
          return () => h('span', `正在识别题目, 预计 ${currentSeconds.value}/${totalSeconds} 秒`);
        }
      };

      const notificationInstance = ElNotification({
        title: '提示',
        message: h(NotificationContent),
        duration: 0,         // 持续显示，直至手动关闭
        type: 'info'      // 设置消息类型
      });

      const timer = setInterval(() => {
        currentSeconds.value++;
      }, 1000);

      this.$emit('isExtracting', areaIdx)
      this.$axios.post("/api/docCorrectConfig/extraQs", form).then(res => {
        let qsLen = 0
        let qss = res.data.map(qs => {
          return {
            name: `问题${++qsLen}`,
            question: qs.question,
            qsInfo: qs.questionInfo,
            answer: qs.answer,
            questionType: qs.questionType,
            score: qs?.score || 1,
            isAdditional: 1,
            flagArea: {
              x: 0,
              y: 0,
              width: 50,
              height: 50,
              rotate: 0,
              scaleX: 1,
              scaleY: 1
            },
            reviewType: 2,
            defaultReview: '',
            scoreType: '总分',
            opinion: 2,
            isScorePoint: 1,
            scorePoints: "",
          }
        })
        let areaType = this.areas[areaIdx].areaType;
        if (areaType === 4) {
          this.$message.warning("题目识别不支持区域识别")
        } else {
          this.areas[areaIdx].questions = qss
          this.areas[areaIdx].commonQuestionType = qss?.[0]?.questionType || '通用';

          this.$message.success("题目识别成功")
          // 判断题目数量看是否提示
          let isMultipleChoice = false;
          qss.forEach(qs => {
            if (qs.answer === 'A' || qs.answer === 'B' || qs.answer === 'C' || qs.answer === 'D' || qs.answer === 'E') {
              isMultipleChoice = true;
            }
          });
          if (isMultipleChoice && qss.length > 10) {
            ElNotification({
              title: '题目数量过多',
              message: '建议 选择题 每个框小于等于 10 题',
              duration: 5000,
              type: 'warning'
            });
          } else if (!isMultipleChoice && qss.length > 5) {
            ElNotification({
              title: '题目数量过多',
              message: '建议 填空题 建议每次配置小于等于 5 题',
              duration: 5000,
              type: 'warning'
            });
          }
        }

        this.$emit("stopOcr")
      }).finally(() => {
        clearInterval(timer);
        notificationInstance.close();

        this.$emit('isExtracting', areaIdx)
      })
    },
    areaData() {
      let areas = []
      this.areas.forEach(area => {
        let a = Object.assign({}, area)
        delete a.expand
        areas.push(a)
      })
      return areas
    },
    closeExpand() {
      this.areas.forEach(area => {
        area.expand = []
      })
      this.expandAreas = []
    },
    copyArea(areaIdx) {
      let area = JSON.parse(JSON.stringify(this.areas[areaIdx]))
      this.areas.push(area)
      this.$message.success('已复制区域')
    },
    copyQs(areaIdx, qsIdx) {
      let qs = JSON.parse(JSON.stringify(this.areas[areaIdx].questions[qsIdx]))
      this.areas[areaIdx].questions.splice(qsIdx + 1, 0, qs)
      // 关闭原题目的展开状态，展开新插入的题目
      this.areas[areaIdx].expand = this.areas[areaIdx].expand.filter(e => e !== qsIdx)
      this.areas[areaIdx].expand.push(qsIdx + 1)
    },
    addAnswerCardArea() {
      this.areas.push({
        areaType: 2,
        enabled: true,
        expand: [],
        questions: [
          {
            score: 1,
            scoreType: '总分',
            flagArea: {
              x: 0,
              y: 0,
              width: 50,
              height: 50,
              rotate: 0,
              scaleX: 1,
              scaleY: 1
            },
            optionArea: null,
            choiceMode: 0,
            choiceNum: 1,
            answer: [],
          }
        ],
      })
    },
    syncAreaScore(areaIdx, qsIdx) {
      const score = this.areas[areaIdx].questions[qsIdx].score
      this.areas[areaIdx].questions.forEach((qs, qsIdx) => {
        qs.score = score
      })
      this.$message.success('分数已同步')
      this.$forceUpdate()
    },
    syncAreaScoreByScore(areaIdx, score) {
      this.areas[areaIdx].questions.forEach((qs, qsIdx) => {
        qs.score = score
      })
      this.$message.success('分数已同步')
      this.$forceUpdate()
    },
    syncAreaType(areaIdx, qsIdx) {
      const scoreType = this.areas[areaIdx].questions[qsIdx].scoreType
      this.areas[areaIdx].questions.forEach((qs, qsIdx) => {
        qs.scoreType = scoreType
      })
      this.$message.success('类型已同步')
      this.$forceUpdate()
    },
    syncAreaTypeByAreaType(areaIdx, scoreType, isAsyncAllArea) {
      if (isAsyncAllArea) {
        this.areas.forEach((area, areaIdx) => {
          area.questions.forEach((qs, qsIdx) => {
            qs.scoreType = scoreType
          })
        })
      } else {
        this.areas[areaIdx].questions.forEach((qs, qsIdx) => {
          qs.scoreType = scoreType
        })
      }

      this.$message.success('类型已同步')
      this.$forceUpdate()
    },
    // 判断是否应该显示答案提示
    shouldShowAnswerHint(questionType) {
      const hintTypes = ['单选题', '多选题', '判断题', '涂卡题', '涂卡选择题', '涂卡判断题'];
      return hintTypes.includes(questionType);
    },
    // 获取答案提示文本
    getAnswerHintText(questionType) {
      if (questionType === '单选题' || questionType === '多选题' || questionType === '涂卡选择题') {
        return '答案格式应该为A、B、C、D、E等';
      } else if (questionType === '判断题' || questionType === '涂卡判断题') {
        return '答案格式应该为T、√、[■][×]或者F、×、[√][■]';
      } else if (questionType === '涂卡题') {
        // 涂卡题可能包含选择题和判断题，这里提供通用提示
        return '答案格式应该为A、B、C、D、E等或T、√、[■][×]、F、×、[√][■]';
      }
      return '';
    },
    // 获取答案选项
    getAnswerOptions(questionType) {
      if (questionType === '单选题' || questionType === '多选题' || questionType === '涂卡选择题') {
        return ['A', 'B', 'C', 'D', 'E'];
      } else if (questionType === '判断题' || questionType === '涂卡判断题') {
        return ['T', '√', '[■][×]', 'F', '×', '[√][■]'];
      } else if (questionType === '涂卡题') {
        return ['A', 'B', 'C', 'D', 'E', 'T', '√', '[■][×]', 'F', '×', '[√][■]'];
      }
      return [];
    },
    // 复制答案选项
    copyAnswerOption(option) {
      // 使用现代浏览器的 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(option).then(() => {
          this.$message.success(`已复制: ${option}`);
        }).catch(() => {
          this.fallbackCopyTextToClipboard(option);
        });
      } else {
        // 降级方案
        this.fallbackCopyTextToClipboard(option);
      }
    },
    // 降级复制方案
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.top = "0";
      textArea.style.left = "0";
      textArea.style.position = "fixed";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        this.$message.success(`已复制: ${text}`);
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }
      document.body.removeChild(textArea);
    },
  }
}
</script>
<style lang="scss" scoped>
.deduction-form {
  :deep(.el-form-item) { margin-bottom: 12px; }
}
.coords { margin-right: 6px; }

.primary-button {
  background: #1677FF;
  border-radius: 4px;
  padding: 1px 10px;
  transition: all 0.3s ease;
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);

  &:hover {
    background: #4a90ff !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4);

    .text {
      color: #FFFFFF !important; /* 确保文字保持白色 */
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15);
  }

  .text {
    color: #FFFFFF;
    font-size: 13px;
    font-weight: 700; /* 加粗字体 */
    opacity: 0.95;
  }
}

.card-header {
  display: flex;
  align-items: center;

  .right-actions {
    margin-left: auto;
  }
}


.handle {
  float: left;
  padding-top: 8px;
  padding-bottom: 8px;
}

.handleCollapse {
  float: left;
  padding-top: 8px;
  padding-bottom: 8px;
}

.card-content {

  .qs-card {

    .qs-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 0 0 0 20px;
      border-bottom: 1px solid #ebeef5;

      .right-actions {
        width: 105px;
        height: 24px;
        margin-left: auto;
      }

      .qs-ocr {
        width: 105px;
        height: 27px;
        background-image: linear-gradient(90deg, #A45DFF 0%, #3772FF 100%);
        border-radius: 4px;
        align-items: center;

        .icon {
          width: 16px;
          height: 16px;
        }

        .text {
          font-weight: 400;
          font-size: 13px;
          color: #FFFFFF;
          text-align: center;
          line-height: 22px;
          opacity: 0.95;
          margin-left: 6px;
        }
      }

      // 题目识别按钮 - 待识别状态（灰色）
      .qs-ocr-pending {
        width: 105px !important;
        height: 27px !important;
        background-color: #6B7280 !important;
        background: #6B7280 !important;
        border-radius: 4px !important;
        align-items: center !important;
        border: none !important;

        .icon {
          width: 16px;
          height: 16px;
        }

        .text {
          font-weight: 400;
          font-size: 13px !important;
          color: #FFFFFF !important;
          text-align: center;
          line-height: 22px;
          opacity: 0.95 !important;
          margin-left: 6px;
        }
      }

      // 题目识别按钮 - 已识别状态（绿色）
      .qs-ocr-completed {
        width: 105px !important;
        height: 27px !important;
        background-color: #10B981 !important;
        background: #10B981 !important;
        border-radius: 4px !important;
        align-items: center !important;
        border: none !important;

        .icon {
          width: 16px;
          height: 16px;
        }

        .text {
          font-weight: 400;
          font-size: 13px !important;
          color: #FFFFFF !important;
          text-align: center;
          line-height: 22px;
          opacity: 0.95 !important;
          margin-left: 6px;
        }
      }

      // 选取区域按钮 - 未选取状态（灰色）
      .area-unselected-button {
        background-color: #6B7280 !important;
      }

      // 选取区域按钮 - 正在选取状态（灰色）
      .area-selecting-button {
        background-color: #6B7280 !important;
      }

      // 选取区域按钮 - 已选取状态（绿色）
      .area-selected-button {
        background-color: #10B981 !important;
      }


      .deduction-area-unselected-button {
        background-color: #6B7280 !important;
        margin-left: 10px;
      }

      .deduction-area-selecting-button {
        background-color: #6B7280 !important;
        margin-left: 10px;
      }

      .deduction-area-selected-button {
        background-color: #10B981 !important;
        margin-left: 10px;
      }

      .card-header-select {
        width: 80px;
        margin-left: 10px;
      }
    }

    .qs-form {
      :deep(.el-form-item--small) {
        margin-bottom: 10px;
      }

      :deep(.el-divider--horizontal) {
        margin: 15px 0;
      }
    }
  }
}

// 答案提示样式
.answer-hint-container {
  margin: 10px 0;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;

  .answer-hint-text {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .answer-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .answer-option {
      display: inline-block;
      padding: 4px 8px;
      background-color: #007bff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.2s ease;
      user-select: none;

      &:hover {
        background-color: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 123, 255, 0.2);
      }
    }
  }
}
</style>