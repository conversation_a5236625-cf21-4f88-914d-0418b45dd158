<template>
  <el-dialog v-model="dialogVisible" title="添加到测试集" width="800px" @close="onClose">
    <!-- 区域选择 -->
    <div style="margin-bottom: 20px;">
      <h4>选择要添加的区域：</h4>
      <el-checkbox-group v-model="selectedAreas">
        <el-checkbox 
          v-for="area in areas" 
          :key="area.areaIdx" 
          :label="area.areaIdx"
          style="display: block; margin-bottom: 8px;"
        >
          {{ area.name }} ({{ area.questionType }})
        </el-checkbox>
      </el-checkbox-group>
    </div>

    <!-- 测试集选择方式 -->
    <div style="margin-bottom: 20px;">
      <h4>选择测试集：</h4>
      <el-radio-group v-model="testSetMode">
        <el-radio label="new">新增测试集</el-radio>
        <el-radio label="existing">选择现有测试集</el-radio>
      </el-radio-group>
    </div>

    <!-- 新增测试集表单 -->
    <div v-if="testSetMode === 'new'" style="margin-bottom: 20px;">
      <el-form ref="newTestSetForm" :model="newTestSetForm" :rules="newTestSetRules" label-width="120px">
        <el-form-item label="测试集名称" prop="name">
          <el-input v-model="newTestSetForm.name" placeholder="请输入测试集名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="newTestSetForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入描述"
            maxlength="512"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 选择现有测试集 -->
    <div v-if="testSetMode === 'existing'" style="margin-bottom: 20px;">
      <el-form inline>
        <el-form-item label="搜索测试集">
          <el-input 
            v-model="searchKeyword" 
            placeholder="请输入测试集名称" 
            @input="onSearchChange"
            clearable
            style="width: 300px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchTestSets">搜索</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 测试集列表 -->
      <el-table 
        v-loading="testSetLoading"
        :data="testSetList" 
        style="width: 100%; margin-top: 10px;"
        highlight-current-row
        @current-change="onTestSetSelect"
      >
        <el-table-column prop="name" label="测试集名称" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="questionTypes" label="题型" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="180" />
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-if="testSetTotal > 0"
        background
        layout="prev, pager, next"
        :page-size="testSetPageSize"
        :current-page="testSetPageNumber"
        :total="testSetTotal"
        @current-change="onTestSetPageChange"
        style="margin-top: 15px; text-align: center;"
      />
    </div>

    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="onSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'TestSetDialog',
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      
      // 区域数据
      areas: [],
      selectedAreas: [],
      
      // 测试集模式
      testSetMode: 'new', // 'new' | 'existing'
      
      // 新增测试集表单
      newTestSetForm: {
        name: '',
        description: ''
      },
      newTestSetRules: {
        name: [{ required: true, message: '测试集名称不能为空', trigger: 'blur' }]
      },
      
      // 现有测试集搜索
      searchKeyword: '',
      testSetLoading: false,
      testSetList: [],
      testSetTotal: 0,
      testSetPageNumber: 1,
      testSetPageSize: 10,
      selectedTestSet: null,
      
      // 当前试卷信息
      currentRecord: null,
      searchTimer: null
    }
  },
  methods: {
    show(record, areas) {
      this.currentRecord = record;
      this.areas = areas || [];
      this.selectedAreas = [];
      this.testSetMode = 'new';
      this.resetForms();
      this.dialogVisible = true;
      
      // 如果是选择现有测试集模式，加载测试集列表
      if (this.testSetMode === 'existing') {
        this.searchTestSets();
      }
    },
    
    onClose() {
      this.dialogVisible = false;
      this.resetForms();
    },
    
    resetForms() {
      this.newTestSetForm = {
        name: '',
        description: ''
      };
      this.searchKeyword = '';
      this.testSetList = [];
      this.selectedTestSet = null;
      this.testSetPageNumber = 1;
      if (this.$refs.newTestSetForm) {
        this.$refs.newTestSetForm.clearValidate();
      }
    },
    
    onSearchChange() {
      // 防抖搜索
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.searchTimer = setTimeout(() => {
        this.searchTestSets();
      }, 500);
    },
    
    async searchTestSets() {
      this.testSetLoading = true;
      try {
        console.log('搜索测试集请求参数:', {
          pageNumber: this.testSetPageNumber,
          pageSize: this.testSetPageSize,
          name: this.searchKeyword
        });

        const response = await this.$axios.get('/api/testset/page', {
          params: {
            pageNumber: this.testSetPageNumber,
            pageSize: this.testSetPageSize,
            name: this.searchKeyword
          }
        });

        console.log('搜索测试集响应:', response.data);

        // 根据API文档处理响应
        if (response.data.code === 200) {
          const pageObj = response.data.data || {};
          this.testSetList = pageObj.records || [];
          this.testSetTotal = pageObj.total || 0;
        } else {
          this.$message.error(response.data.message || '获取测试集列表失败');
        }
      } catch (error) {
        console.error('搜索测试集失败:', error);
        if (error.response) {
          console.error('错误响应:', error.response.data);
          this.$message.error(error.response.data.message || `API调用失败: ${error.response.status}`);
        } else {
          this.$message.error('网络错误或服务器无响应');
        }
      } finally {
        this.testSetLoading = false;
      }
    },
    
    onTestSetPageChange(page) {
      this.testSetPageNumber = page;
      this.searchTestSets();
    },
    
    onTestSetSelect(row) {
      this.selectedTestSet = row;
    },
    
    async onSubmit() {
      if (this.selectedAreas.length === 0) {
        this.$message.warning('请至少选择一个区域');
        return;
      }
      
      if (this.testSetMode === 'new') {
        // 验证新增测试集表单
        try {
          await this.$refs.newTestSetForm.validate();
        } catch (error) {
          return;
        }
      } else if (this.testSetMode === 'existing') {
        if (!this.selectedTestSet) {
          this.$message.warning('请选择一个测试集');
          return;
        }
      }
      
      this.submitLoading = true;
      
      try {
        let testSetId;
        
        if (this.testSetMode === 'new') {
          // 创建新测试集
          testSetId = await this.createNewTestSet();
        } else {
          // 使用现有测试集
          testSetId = this.selectedTestSet.id;
        }
        
        // 添加图片到测试集
        await this.addImagesToTestSet(testSetId);
        
        this.$message.success('添加到测试集成功');
        this.onClose();
        this.$emit('success');
        
      } catch (error) {
        console.error('添加到测试集失败:', error);
        this.$message.error('添加到测试集失败');
      } finally {
        this.submitLoading = false;
      }
    },
    
    async createNewTestSet() {
      // 收集选中区域的题型
      const selectedQuestionTypes = this.areas
        .filter(area => this.selectedAreas.includes(area.areaIdx))
        .map(area => area.questionType)
        .filter(Boolean);

      const uniqueQuestionTypes = [...new Set(selectedQuestionTypes)];

      const testSetData = {
        name: this.newTestSetForm.name,
        description: this.newTestSetForm.description,
        questionTypes: uniqueQuestionTypes.join(','),
        recordModelSettingEntityList: [] // 暂时为空数组
      };

      console.log('创建测试集请求数据:', testSetData);

      try {
        const response = await this.$axios.post('/api/testset/add', testSetData);
        console.log('创建测试集响应:', response.data);

        // 根据API文档处理响应
        if (response.data.code === 200) {
          // 返回创建的测试集对象中的id
          return response.data.data.id;
        } else {
          throw new Error(response.data.message || '创建测试集失败');
        }
      } catch (error) {
        console.error('创建测试集API调用失败:', error);
        if (error.response) {
          console.error('错误响应:', error.response.data);
          throw new Error(error.response.data.message || `API调用失败: ${error.response.status}`);
        }
        throw error;
      }
    },
    
    async addImagesToTestSet(testSetId) {
      const promises = this.selectedAreas.map(async (areaIdx) => {
        const area = this.areas.find(a => a.areaIdx === areaIdx);
        if (!area) return;

        const imageData = {
          testSetId: testSetId,
          imgUrl: area.imgUrl || '',
          questionType: area.questionType || '',
          fromRecordId: this.currentRecord.id,
          fromRecordAreaIdx: areaIdx,
          useQuestionDetail: true
        };

        // 如果有题目详情和答案，也一并保存
        if (area.questionDetail) {
          imageData.questionDetail = area.questionDetail;
        }
        if (area.rightAnswer) {
          imageData.rightAnswer = area.rightAnswer;
        }

        const response = await this.$axios.post('/api/testsetImage/add', imageData);
        console.log('添加图片到测试集响应:', response.data);

        // 根据API文档处理响应
        if (response.data.code !== 200) {
          throw new Error(response.data.message || `添加区域${area.name}到测试集失败`);
        }

        return response;
      });

      await Promise.all(promises);
    }
  }
}
</script>

<style scoped>
.el-checkbox {
  margin-right: 0;
}

.el-table {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.current-row) {
  background-color: #ecf5ff;
}
</style>
