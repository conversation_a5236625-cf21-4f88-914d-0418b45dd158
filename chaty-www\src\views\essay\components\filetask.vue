<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-image src="/icon/16.png" class="icon"></el-image>
          <el-text class="text">作文批改</el-text>
          <el-text v-if="essayConfig" style="font-weight: bold;font-size: 15px;margin-right: 5px">{{ getConfigText() }}</el-text>
          <el-link @click="jumpDetail" >
            <el-text style="font-weight: bold;font-size: 15px;">表格展开</el-text>
          </el-link>
        </div>
      </template>
      <template #extra>
        <div>
          <el-button v-if="task && task.status !== 3" class="header-action correct-button"
                     @click="correctTask(task.id)">批改
          </el-button>
          <el-button class="header-action" type="primary" icon="Download" @click="downloadOrigin">下载原卷
          </el-button>
          <el-button class="header-action" type="primary" icon="Download" @click="onStatsDownloadSubmit">统计结果
          </el-button>
          <el-button class="header-action" type="primary" icon="Download" @click="downloadEssayReport">作文报告下载
          </el-button>
          <el-button class="header-action" type="primary" icon="RefreshRight" @click="initCropper">刷新预览</el-button>
        </div>
      </template>
    </el-page-header>
    <div class="main-content">
      <div class="left-content" v-if="!loadingRecord">
        <record ref="recordComponent"
                :task-id="recordId"
                :select-row-index="nowCropper.paperIdx"
                is-essay
                show-student-name
                is-task-selected
                :parent-loaded-data="taskData"
                @loadFinish="loadRecordFinish"
                @loadConfigFinish="loadConfigFinish"
                @review="reviewTask"
                @errorCorrection="errorCorrection"
                @freezeKeyboard="changeIsFreezeKeyboard"
        />
      </div>
      <div class="right-content">
        <cropper ref="cropper" class="canvas" :loading="loadingCropper"
                 :left-top-text="nowCropper.pageIdx + '/' + cropperDetail[nowCropper.taskId]?.docUrlList.length"
                 :is-freeze-keyboard="isFreezeKeyboard"
                 style="margin-top: 15px" @arrow-key-pressed="arrowKeyPressed">
        </cropper>
        <div class="pagination">
          <el-pagination
              background
              layout="prev, pager, next, jumper"
              :total="nowCropper.taskId ? cropperDetail[nowCropper.taskId].docUrlList.length : 0"
              v-model:current-page="nowCropper.pageIdx"
              :page-size="1"
              class="right"
          />
        </div>
      </div>
    </div>

    <task-form ref="taskForm" @onClose="loadData"/>
    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <img src="/right.svg" id="flagimg" style="display: none;"/>
    <img src="/wrong.svg" id="wrongImg" style="display: none;"/>
    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <img class="cropper-img" :src="imgDataUrl" id="drawImg" style="display: none;">
  </div>
</template>

<script>
import {useUserStore} from "@/store/index";
import {mapState} from "pinia";
import TaskForm from '@/views/doccorrect/components/taskform.vue';
import Cropper from '@/components/cropper/forPreview.vue';
import {drawRect} from "@/utils/imgUtil";
import Record from "../../common/record.vue";
import * as pdfjsLib from 'pdfjs-dist';

export default {
  components: {
    Record,
    Cropper,
    TaskForm,
  },
  data() {
    return {
      taskId: this.$route.params.fileId,
      recordId: '',
      statusOptions: {
        1: {value: 1, label: "待批改"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      refresher: null,
      statusStats: {
        1: 0,
        2: 0,
        3: 0,
        total: 0,
      },
      cropperDetail: {},
      nowCropper: {
        // 当前选中的task的id，第几个paper和第几页（+1的关系）,当前docUrl
        taskId: null,
        paperIdx: null,
        pageIdx: null,
        docUrl: null
      },
      changePaginationLoading: false,
      loadingCropper: false,
      isRefreshingDataAndDontNeedInitCropper: false,
      firstLoadNeedInitCropper: true,
      imgDataUrl: null,
      loadingRecord: true,
      task: null,
      haveCorrect: false,
      essayConfig: null,
      scoringSchemes: [
        {label: "A/B/C制", value: "A/B/C"},
        {label: "分数制", value: "score"},
      ],
      essayStyles: [
        {label: "英文作文", value: "englishEssay"},
        {label: "语文作文", value: "chineseEssay"},
        {label: "议论文", value: "argumentative"},
        {label: "说明文", value: "expository"},
        {label: "记叙文", value: "narrative"},
        {label: "描写文", value: "descriptive"},
        {label: "抒情文", value: "lyrical"},
        {label: "应用文", value: "practical"},
      ],
      imagesList: {},
      loadingImagesList: [],
      isFreezeKeyboard: false,
      taskData: {},
      timer: null
    }
  },
  created() {
    this.loadRecord();
  },
  watch: {
    "nowCropper.pageIdx"(val) {
      this.nowCropper.paperIdx = val - 1;
      this.nowCropper.docUrl = this.nowCropper.taskId ? this.cropperDetail[this.nowCropper.taskId].docUrlList[val - 1] : null;
      this.initCropper();
    },
    '$route.params.fileId'(newId, oldId) {
      this.taskId = newId;
      if (newId !== oldId) {
        this.loadRecord();
      }
    },
  },
  computed: {
    ...mapState(useUserStore, ["getUser"]),
  },
  methods: {
     downloadOrigin() {
      let loadingMessage = this.$message({
        message: "正在下载原卷...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.get("/api/docCorrectFile/getById?id=" + this.task.fileId).then(res => {
        if ('url' in res.data && res.data.url !== undefined) {
          this.downloadFile(res.data.url, `${res.data.name}(原卷).pdf`);
          loadingMessage.close()
        } else{
          this.$message.warning("老版不支持下载原卷");
          loadingMessage.close()
        }
      }).error(()=>{
        loadingMessage.close()
      })
    },
    changeIsFreezeKeyboard(e) {
      this.isFreezeKeyboard = e;
    },
    jumpDetail() {
      this.$router.push({path: '/docrecord/' + this.task.id});
    },
    arrowKeyPressed(e) {
      let pageIdx = this.nowCropper.pageIdx;
      if (e === 1) {
        if (pageIdx === this.cropperDetail[this.nowCropper.taskId].docUrlList.length) {
          // this.nowCropper.pageIdx = 1;
          // 到头之后不再重新
          this.$message.warning('已经是最后一页');
        } else {
          this.nowCropper.pageIdx = e + pageIdx;
        }
      } else if (e === -1) {
        if (pageIdx === 1) {
          // this.nowCropper.pageIdx = this.cropperDetail[this.nowCropper.taskId].docUrlList.length;
          // 到头之后不再重新
          this.$message.warning('已经是第一页');
        } else {
          this.nowCropper.pageIdx = e + pageIdx;
        }
      }
    },
    getConfigText() {
      if (!this.essayConfig) return "";
      let essayStyle = this.essayConfig.essayStyle ? this.essayStyles.find(item => item.value === this.essayConfig.essayStyle)?.label : '';
      let scoringScheme = this.essayConfig.scoringScheme ? this.scoringSchemes.find(item => item.value === this.essayConfig.scoringScheme)?.label : '';
      let score = '满分:' + this.essayConfig.score;
      let wordCount = '字数要求:' + this.essayConfig.wordCount;
      return `${essayStyle}  ${scoringScheme}  ${score}  ${wordCount}`;
    },
    loadConfigFinish(e) {
      this.essayConfig = e;
    },
    correctTask(taskId) {
      const store = useUserStore();
      let form = {
        id: taskId,
        aimodel: store.getDefaultCorrectModal,
        ocrType: '2',
        responseFormat: true,
        jsonobject: store.getDefaultJsonObject,
        jsonschema: store.getDefaultJsonSchema,
      }
      this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
        this.$message.success("提交成功")
        this.loadData()
        this.loadStatusStats()
      })
    },
    downloadEssayReport() {
      if (!this.task) {
        this.$message.error("未找到该任务");
        return;
      }
      let loadingMessage = this.$message({
        message: "正在生成批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectRecord/download/essayReport", {
        taskId: this.task.id
      }).then(res => {
        return this.downloadFile(res.data.fileUrl, `${this.task.name} (作文报告).pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
    loadRecord() {
      if (!this.taskId) return;
      this.$axios.get("/api/docCorrectFile/getEssayDeep?id=" + this.taskId).then(res => {

        this.task = res.data.task
        this.taskData = res.data;
        this.recordId = res.data.records[0].id;
        this.loadingRecord = false;

        if ('config' in this.taskData && 'config' in this.taskData.config && !('configObj' in this.taskData.config)) {
          this.taskData.config.configObj = JSON.parse(this.taskData.config.config)
        }
        if ('config' in this.taskData && 'areas' in this.taskData.config && !('areasObj' in this.taskData.config)) {
          this.taskData.config.areasObj = JSON.parse(this.taskData.config.areas)
        }
        console.log(this.taskData)
        if (this.task.status === 2) {
          if (this.timer) {
            clearInterval(this.timer);
          }
          this.timer = null;
        }
        
        if (res.data.records[0].status === 3) {
          this.$emit('setActiveStep', 3);
        } else if (res.data.records[0].status === 1) {
          // 只能自动批改一次
          if (!this.haveCorrect) {
            this.haveCorrect = true;
            this.$axios.post(`/api/docCorrectFile/correct`, {
              id: this.taskId,
            }).then(res => {
              this.timer = setTimeout(() => {
                this.loadRecord()
              }, 1000)
            })
          }
        }
      })
    },
    reviewTask(options) {
      this.nowCropper.paperIdx = options.paperIdx;
      this.nowCropper.pageIdx = options.paperIdx + 1;
      this.nowCropper.docUrl = options.docUrl;
      this.nowCropper.taskId = options.taskId;
    },
    errorCorrection(data) {
      let options = data.options;
      this.cropperDetail[options.taskId].qsPositionDetail[options.docurl].detail = data.detail;
      this.nowCropper.paperIdx = options.paperIdx;
      this.nowCropper.pageIdx = options.paperIdx + 1;
      this.nowCropper.docUrl = options.docurl;
      this.nowCropper.taskId = options.taskId;
    },
    async convertPdfUrlToBase64(pdfUrl, nextDocUrl, nextNextDocUrl) {
      if (this.isRefreshingDataAndDontNeedInitCropper) return;
      this.loadingCropper = true;
      try {
        if (pdfUrl in this.imagesList) {
          this.imgDataUrl = this.imagesList[pdfUrl];
          this.loadingCropper = false;
          this.preview();
          this.loadNextImage(nextDocUrl, nextNextDocUrl)
        } else {
          const response = await fetch(this.$fileserver.fileurl(pdfUrl));
          if (!response.ok) {
            throw new Error('Failed to fetch PDF');
          }
          const blob = await response.blob();
          const pdfData = await blob.arrayBuffer();
          const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
          const totalPages = pdfDoc.numPages;
          const images = [];
          const targetDPI = 300;
          const scaleFactor = targetDPI / 36;  // PDF 默认分辨率通常是 72 DPI
          if (totalPages === 1) {
            const page = await pdfDoc.getPage(1);
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            const viewport = page.getViewport({scale: scaleFactor});
            canvas.width = viewport.width;
            canvas.height = viewport.height;
            await page.render({
              canvasContext: context,
              viewport: viewport,
            }).promise;
            const imgDataUrl = canvas.toDataURL('image/jpeg');
            images.push(imgDataUrl);
            this.loadingCropper = false;
            this.imgDataUrl = imgDataUrl;
            this.imagesList[pdfUrl] = imgDataUrl;
            this.preview();
          }
          this.loadNextImage(nextDocUrl, nextNextDocUrl)
        }
      } catch (error) {
        console.error('PDF 转换失败', error);
      }
    },
    async loadNextImage(nextDocUrl, nextNextDocUrl) {
      if (!nextDocUrl) return;
      if (nextDocUrl in this.imagesList) return;
      if (this.loadingImagesList.includes(nextDocUrl)) return;
      try {
        this.loadingImagesList.push(nextDocUrl);
        const response = await fetch(this.$fileserver.fileurl(nextDocUrl));
        this.loadingImagesList = this.loadingImagesList.filter(item => item !== nextDocUrl);
        if (!response.ok) {
          throw new Error('Failed to fetch PDF');
        }
        const blob = await response.blob();
        const pdfData = await blob.arrayBuffer();
        const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
        const totalPages = pdfDoc.numPages;
        const images = [];
        const targetDPI = 300;
        const scaleFactor = targetDPI / 36;  // PDF 默认分辨率通常是 72 DPI
        if (totalPages === 1) {
          const page = await pdfDoc.getPage(1);
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          const viewport = page.getViewport({scale: scaleFactor});
          canvas.width = viewport.width;
          canvas.height = viewport.height;
          await page.render({
            canvasContext: context,
            viewport: viewport,
          }).promise;
          const imgDataUrl = canvas.toDataURL('image/jpeg');

          const keys = Object.keys(this.imagesList);
          if (keys.length > 10) {
            const firstKey = keys[0];
            delete this.imagesList[firstKey];
          }
          this.imagesList[nextDocUrl] = imgDataUrl;
          this.loadNextImage(nextNextDocUrl, '');
        }

      } catch (error) {
        console.error('加载下一个图像失败', error);
      }
    },
    preview(skip) {
      let detail = this.cropperDetail[this.nowCropper.taskId].qsPositionDetail[this.nowCropper.docUrl].detail;
      let answers = [];
      let areas = [];
      detail.forEach(area => {
        if (area.flagArea) {
          answers.push({
            area: area.flagArea,
            text: area.answer + "分",
            isEssay: area.isEssay,
          })
        }
      })
      this.$refs.cropper.setImg(this.imgDataUrl, areas, answers)
    },
    loadRecordFinish(data) {
      this.cropperDetail[data.taskId] = {
        docUrlList: data.docList,
        qsPositionDetail: data.data,
        areaConfig: data.areaConfig
      }
      if (!this.nowCropper.taskId || this.changePaginationLoading) {
        let needCropper = false;
        if (this.changePaginationLoading) {
          // 由用户切换套卷触发的也要初始化0
          this.changePaginationLoading = false;
          if (!this.nowCropper.pageIdx || this.nowCropper.pageIdx === 1) {
            // 需要手动触发
            needCropper = true;
          }
        }
        // 当前没有选中任何，要初始化0
        this.nowCropper.paperIdx = 0;
        this.nowCropper.pageIdx = 1;
        this.nowCropper.taskId = data.taskId;
        this.nowCropper.docUrl = data.docList[0];
        if (needCropper) this.initCropper();
      }
    },
    initCropper() {
      if (this.nowCropper.docUrl) {
        this.nowCropper.docUrl = this.cropperDetail[this.nowCropper.taskId].docUrlList[this.nowCropper.paperIdx];
        let nextDocUrl = '';
        if (this.nowCropper.paperIdx + 1 < this.cropperDetail[this.nowCropper.taskId].docUrlList.length) {
          nextDocUrl = this.cropperDetail[this.nowCropper.taskId].docUrlList[this.nowCropper.paperIdx + 1];
        }
        let nextNextDocUrl = '';
        if (this.nowCropper.paperIdx + 2 < this.cropperDetail[this.nowCropper.taskId].docUrlList.length) {
          nextNextDocUrl = this.cropperDetail[this.nowCropper.taskId].docUrlList[this.nowCropper.paperIdx + 2];
        }
        this.convertPdfUrlToBase64(this.nowCropper.docUrl, nextDocUrl, nextNextDocUrl).then(res => {
        })
      }
    },
    goBack() {
      this.$router.back();
    },
    onStatsDownloadSubmit() {
      let loadingMessage = this.$message({
        message: "正在生成统计结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      const store = useUserStore();
      this.$axios.post("/api/docCorrectRecord/download/essayAnalyticalReport", {
        taskId: this.task.id,
        aimodel: store.getDefaultCorrectModal
      }).then(res => {
        this.downloadFile(res.data.pdfOutPath, `${this.task?.name + " " || ""}统计结果.pdf`);
        this.downloadFile(res.data.mdOutPath, `${this.task?.name + " " || ""}统计结果.md`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
    downloadFile(url, name) {
      // 使用fetch获取文件内容
      return fetch(this.$fileserver.fileurl(url))
          .then(response => response.blob())
          .then(blob => {
            // 如果需要下载，可以使用前面提到的下载代码
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = URL.createObjectURL(blob);
            a.download = name;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(a.href);
          })
          .catch(error => {
            console.error('发生错误:', error);
          });
    },
    onDownloadSubmit(options) {
      let form = {
        isPreview: true,
        scoreMerge: true,
        showQsScore: true,
        taskIds: this.tasks.map(task => task.id).reverse()
      }
      Object.assign(form, options);
      let loadingMessage = this.$message({
        message: "正在生成批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/download/reviewed", form).then(res => {
        return this.downloadFile(res.data.fileUrl, `${this.file?.name + " " || ""}批改结果(${form.isPreview ? '含原卷' : '不含原卷'}).pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.el-table {
  table-layout: fixed;
}

:deep(.el-page-header__header) {
  width: 100% !important;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    width: 100%;
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;
    align-items: center;

    .icon {
      width: 28.28px;
      height: 22.89px;
    }

    .text {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin: 0 10px
    }

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  .main-content {
    display: flex;
    height: calc(100vh - 80px);
    width: 100%;
    gap: 20px;

    .left-content {
      flex: 1;
      min-width: 0;
    }

    .right-content {
      width: 50%;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 10px);
      margin-top: 10px;

      .canvas {
        height: 100%;
        width: 100%;
      }

      .pagination {
        height: 50px;
        margin-top: 5px;
        display: flex;
        align-items: center;

        .left {
          width: 200px;
          flex-shrink: 0;
        }

        .right {
          flex-grow: 1;
        }
      }
    }
  }

  // 批改按钮自定义样式
  :deep(.correct-button) {
    background-color: #1677FF !important;
    color: #FFF !important;
    border: none !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;

    &:hover {
      background-color: #4a90ff !important;
      color: #FFF !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3) !important;
    }

    &:focus {
      background-color: #4a90ff !important;
      color: #FFF !important;
    }

    &:active {
      background-color: #1677FF !important;
      color: #FFF !important;
    }
  }
}
</style>