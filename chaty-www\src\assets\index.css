.el-menu-item {
    font-weight: 700;
    font-size: 18px;
}

.el-button>span {
    font-weight: 700;
}

.el-table thead .el-table__cell .cell {
    font-weight: 700;
}

/* 导航栏图标颜色覆盖 - 使用更强的选择器 */
.menu-bar-grid .menu-area-grid .el-menu-item.is-active,
.menu-bar-grid .menu-area-grid .el-menu-item.is-active .el-icon,
.menu-bar-grid .menu-area-grid .el-menu-item.is-active .el-icon svg,
.menu-bar-grid .menu-area-grid .el-menu-item.is-active .icons {
    background-color: #90EE90 !important;
    color: #00ff00 !important;
    fill: #00ff00 !important;
}

.menu-area-grid .el-menu-item .el-icon,
.menu-area-grid .el-menu-item .el-icon svg {
    color: #ffffff !important;
    fill: #ffffff !important;
}

.menu-area-grid .el-menu-item:hover .el-icon,
.menu-area-grid .el-menu-item:hover .el-icon svg {
    color: #333333 !important;
    fill: #333333 !important;
}

/* 特定弹窗的确认按钮样式 - 全局覆盖 */
.areas-selector-dialog .el-button--primary,
.xy-offset-dialog .el-button--primary,
.question-type-model-dialog .el-button--primary {
    background-color: #1677FF !important;
    border-color: #1677FF !important;
    color: #FFF !important;
    transition: all 0.3s ease !important;
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2) !important;
}

.areas-selector-dialog .el-button--primary:hover,
.xy-offset-dialog .el-button--primary:hover,
.question-type-model-dialog .el-button--primary:hover {
    background-color: #4a90ff !important;
    border-color: #4a90ff !important;
    color: #FFF !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4) !important;
}

.areas-selector-dialog .el-button--primary:active,
.xy-offset-dialog .el-button--primary:active,
.question-type-model-dialog .el-button--primary:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15) !important;
}