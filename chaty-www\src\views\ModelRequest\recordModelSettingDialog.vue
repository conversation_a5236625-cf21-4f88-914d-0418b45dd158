<template>
  <el-dialog
      v-model="visible"
      title="按题型编辑模型"
      width="1020px"
      :close-on-click-modal="false"
      @close="onClose"
  >
    <!-- 来源提示 + 工具栏 -->
    <div class="toolbar">
      <el-alert
          v-if="infoBar"
          class="tip"
          type="info"
          :closable="false"
          show-icon
      >
        <template #title>
          {{ infoBar }}
          <el-button type="primary" link @click="reload" :loading="loading">刷新</el-button>
        </template>
      </el-alert>

      <div class="actions">
        <el-button @click="addRow" :loading="loading">新增题型映射</el-button>
        <el-button type="primary" @click="saveAll" :loading="savingAll">批量保存变更</el-button>
      </div>
    </div>

    <!-- 可编辑表格 -->
    <el-table :data="rows" border size="small" v-loading="loading" empty-text="暂无数据">
      <!-- 题型 -->
      <el-table-column label="题目名称" min-width="220">
        <template #default="{ row }">
          <div v-if="row.isNew">
            <el-select
                v-model="row.questionType"
                placeholder="选择或输入题型"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
            >
              <el-option v-for="qt in questionTypes" :key="qt.value" :label="qt.label" :value="qt.value" />
            </el-select>
          </div>
          <div v-else>
            {{ row.questionType || '-' }}
          </div>
        </template>
      </el-table-column>

      <!-- 选择模型 -->
      <el-table-column label="选择模型" min-width="380">
        <template #default="{ row }">
          <el-select
              v-model="row.selectedId"
              clearable
              filterable
              remote
              :remote-method="q => remoteSearchModels(q)"
              :loading="modelsLoading"
              placeholder="搜索并选择模型（留空=未设置）"
              style="width: 100%"
              @change="markDirty(row)"
          >
            <el-option
                v-for="opt in modelOptions"
                :key="opt.id"
                :label="renderModelLabel(opt)"
                :value="opt.id"
            >
              <div class="opt-line">
                <span class="opt-label">{{ opt.name }}</span>
                <span class="opt-sub">{{ opt.modelValue }}</span>
                <el-tag v-if="opt.disabled" size="small" type="danger" effect="plain" style="margin-left:6px">禁用</el-tag>
              </div>
            </el-option>
          </el-select>
          <div class="hint-line">
            <span class="current" v-if="row.modelSetting?.name">
              当前：{{ row.modelSetting.name }}<span v-if="row.modelSetting.modelValue">（{{ row.modelSetting.modelValue }}）</span>
            </span>
            <el-tag v-if="row.dirty" size="small" type="warning" effect="plain" style="margin-left:6px">待保存</el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row, $index }">
          <el-space>
            <el-button
                v-if="row.selectedId"
                type="primary"
                @click="openModelDetail(row)"
            >查看详情</el-button>

            <el-button
                type="primary"
                text
                :disabled="!row.dirty && !row.isNew"
                @click="saveRow(row)"
            >保存</el-button>

            <el-popconfirm
                title="确认删除该映射？"
                @confirm="removeRow(row, $index)"
            >
              <template #reference>
                <el-button type="primary" text>删除</el-button>
              </template>
            </el-popconfirm>
          </el-space>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-space>
        <el-button @click="visible = false">关闭</el-button>
      </el-space>
    </template>
  </el-dialog>

  <!-- 模型详情 -->
  <el-dialog
      v-model="modelDetailVisible"
      width="600px"
      title="模型详情"
      :close-on-click-modal="false"
  >
    <el-descriptions :column="1" border v-loading="modelDetailLoading">
      <el-descriptions-item label="ID">
        {{ modelDetail.id ?? '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="名称">
        {{ modelDetail.name ?? '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="模型值">
        {{ modelDetail.modelValue ?? '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="输出类型">
        {{ getResponseFormat(modelDetail) }}
      </el-descriptions-item>

      <el-descriptions-item label="普通题目两轮询问">
        {{ fmtBool(modelDetail.enableNormalQsTwoRequest) }}
      </el-descriptions-item>

      <el-descriptions-item v-if="!isTwoRound(modelDetail)" label="一轮问询提示词类型">
        {{ fmtPrompt(modelDetail.singleRoundPromptType) }}
      </el-descriptions-item>

      <el-descriptions-item v-if="isTwoRound(modelDetail)" label="第一轮提示词类型">
        {{ fmtPrompt(modelDetail.firstRoundPromptType) }}
      </el-descriptions-item>

      <el-descriptions-item v-if="isTwoRound(modelDetail)" label="第二轮使用JSON比对">
        {{ fmtBool(modelDetail.isSecondRoundJsonComparison) }}
      </el-descriptions-item>

      <el-descriptions-item v-if="isTwoRound(modelDetail) && !modelDetail.isSecondRoundJsonComparison" label="第二轮提示词类型">
        {{ fmtPrompt(modelDetail.secondRoundPromptType) }}
      </el-descriptions-item>

      <el-descriptions-item v-if="isTwoRound(modelDetail)" label="第二轮开启图片">
        {{ fmtBool(modelDetail.isSecondRoundUseImage) }}
      </el-descriptions-item>

      <el-descriptions-item label="图像增强">
        {{ fmtBool(modelDetail.enableImageEnhancement) }}
      </el-descriptions-item>

      <el-descriptions-item v-if="modelDetail.createTime" label="创建时间">
        {{ modelDetail.createTime }}
      </el-descriptions-item>
      <el-descriptions-item v-if="modelDetail.updateTime" label="更新时间">
        {{ modelDetail.updateTime }}
      </el-descriptions-item>

      <el-descriptions-item label="其他参数">
        <json-viewer :value="parseJson(modelDetail.content)" expand-depth="2" />
      </el-descriptions-item>
    </el-descriptions>

    <template #footer>
      <el-button @click="modelDetailVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { JsonViewer } from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'

/**
 * RecordModelSettingEditor.vue
 * - 可编辑映射：新增/修改/删除
 * - 依赖后端：
 *    GET  /api/recordModelSetting/page?type=&fileId=&configPackageId=&testSetId=&pageNumber=&pageSize=     // UPDATED
 *    POST /api/recordModelSetting/add
 *    POST /api/recordModelSetting/update
 *    DELETE /api/recordModelSetting/{id}
 *    POST /api/model-request/selectPage  （远程搜索模型）
 *    GET  /api/prompts/questionTypes     （题型清单）
 *
 *  新增：
 *    - 支持 type: 'file' | 'config' | 'testSet'     // NEW
 *    - 支持 testSetId 参数传递                         // NEW
 */
export default {
  name: 'RecordModelSettingEditor',
  components: {JsonViewer},
  props: {
    fileId: {type: [String, Number], default: null},
    configPackageId: {type: [String, Number], default: null},
    testSetId: {type: [String, Number], default: null}, // NEW
    type: {type: String, default: null}, // 'file' | 'config' | 'testSet' | null  // UPDATED
    autoOpen: {type: Boolean, default: false}
  },
  data() {
    return {
      visible: false,
      loading: false,
      savingAll: false,
      modelsLoading: false,

      // 查询上下文
      q: {type: null, fileId: null, configPackageId: null, testSetId: null}, // UPDATED

      // 行：{ id?, questionType, modelSettingId?, modelSetting?, selectedId?, isNew?, dirty? }
      rows: [],

      // 题型下拉
      questionTypes: [],

      // 模型候选（远程搜索结果）
      modelOptions: [],

      // 详情
      modelDetailVisible: false,
      modelDetailLoading: false,
      modelDetail: {}
    }
  },
  computed: {
    infoBar() {
      // UPDATED: 增加 testSet 来源展示
      if (this.q.type === 'file' && this.q.fileId) return `当前来源：文件（fileId: ${this.q.fileId}）`
      if (this.q.type === 'config' && this.q.configPackageId) return `当前来源：标准卷（configPackageId: ${this.q.configPackageId}）`
      if (this.q.type === 'testSet' && this.q.testSetId) return `当前来源：测试集（testSetId: ${this.q.testSetId}）`
      // 向后兼容：若未显式设置 type，则按已有 id 回退
      if (this.q.fileId) return `当前来源：文件（fileId: ${this.q.fileId}）`
      if (this.q.configPackageId) return `当前来源：标准卷（configPackageId: ${this.q.configPackageId}）`
      if (this.q.testSetId) return `当前来源：测试集（testSetId: ${this.q.testSetId}）`
      return ''
    }
  },
  mounted() {
    // UPDATED: 自动打开时支持 testSetId
    if (this.autoOpen && (this.fileId || this.configPackageId || this.testSetId)) {
      this.openDialog({
        type: this.type || (this.fileId ? 'file' : (this.configPackageId ? 'config' : (this.testSetId ? 'testSet' : null))),
        fileId: this.fileId,
        configPackageId: this.configPackageId,
        testSetId: this.testSetId
      })
    }
  },
  methods: {
    // 兼容旧调用名
    openDialog(args) {
      this.show(args)
    }, // NEW: 避免外部仍调用 openDialog 报错

    // ===== 外部调用 =====
    show({type = null, fileId = null, configPackageId = null, testSetId = null} = {}) { // UPDATED
      this.q.type = type || (fileId ? 'file' : (configPackageId ? 'config' : (testSetId ? 'testSet' : null))) // UPDATED
      this.q.fileId = fileId || null
      this.q.configPackageId = configPackageId || null
      this.q.testSetId = testSetId || null // NEW
      this.visible = true
      this.bootstrap()
    },

    async bootstrap() {
      await Promise.all([this.fetchQuestionTypes(), this.remoteSearchModels('')])
      await this.reload()
    },

    onClose() {
    },

    // ===== 数据加载 =====
    async reload() {
      if (!this.q.fileId && !this.q.configPackageId && !this.q.testSetId) { // UPDATED
        this.rows = []
        return
      }
      try {
        this.loading = true
        const params = new URLSearchParams({
          pageNumber: '1',
          pageSize: '500'
        })
        if (this.q.type) params.set('type', this.q.type) // 'file' | 'config' | 'testSet'
        if (this.q.fileId) params.set('fileId', String(this.q.fileId))
        if (this.q.configPackageId) params.set('configPackageId', String(this.q.configPackageId))
        if (this.q.testSetId) params.set('testSetId', String(this.q.testSetId)) // NEW

        const res = await this.$axios.get('/api/recordModelSetting/page?' + params.toString())
        const list = res?.data?.records || res?.data || []

        this.rows = list.map(it => ({
          id: it.id,
          questionType: it.questionType || it.typeName || it.name || '-',
          modelSettingId: it.modelSettingId,
          modelSetting: it.modelSetting || null,
          selectedId: (it.modelSetting && it.modelSetting.id) || it.modelSettingId || null,
          isNew: false,
          dirty: false
        }))
      } catch (e) {
        console.error(e)
        this.$message.error('加载题型-模型映射失败')
      } finally {
        this.loading = false
      }
    },

    async fetchQuestionTypes() {
      try {
        const res = await this.$axios.get('/api/prompts/questionTypes')
        const arr = Array.isArray(res.data) ? res.data : (res.data?.records || [])
        const list = (arr || []).map((x, idx) => {
          if (typeof x === 'string') return {label: x, value: x}
          return {label: x.label ?? x.name ?? x.value ?? `题型${idx + 1}`, value: x.value ?? x.name ?? x.label}
        })
        // 强制追加“分数识别”
        const force = '分数识别'
        if (!list.some(i => i.label === force || i.value === force)) {
          list.push({label: force, value: force})
        }
        this.questionTypes = list
      } catch (e) {
        console.error(e)
        this.$message.error('加载题型列表失败')
      }
    },

    async remoteSearchModels(query) {
      try {
        this.modelsLoading = true
        const body = {
          name: query || '',
          page: {pageSize: 20, pageNumber: 1, searchCount: false}
        }
        const res = await this.$axios.post('/api/model-request/selectPage', body)
        const records = res?.data?.records || res?.data || []
        this.modelOptions = records.map(r => ({
          id: r.id,
          name: r.name,
          modelValue: r.modelValue,
          disabled: r.disabled === true
        }))
      } catch (e) {
        console.error(e)
      } finally {
        this.modelsLoading = false
      }
    },

    // ===== 行编辑 =====
    addRow() {
      this.rows.unshift({
        id: null,
        questionType: '',
        modelSettingId: null,
        modelSetting: null,
        selectedId: null,
        isNew: true,
        dirty: true
      })
    },

    markDirty(row) {
      row.dirty = true
    },

    async saveRow(row) {
      if (!row.questionType || !row.selectedId) {
        this.$message.warning('请先选择题型和模型')
        return
      }
      try {
        const payload = {
          id: row.id || undefined,
          type: this.q.type || undefined,                 // UPDATED
          fileId: this.q.fileId || undefined,
          configPackageId: this.q.configPackageId || undefined,
          testSetId: this.q.testSetId || undefined,       // NEW
          questionType: row.questionType,
          modelSettingId: row.selectedId
        }
        if (row.isNew) {
          const res = await this.$axios.post('/api/recordModelSetting/add', payload)
          if (res?.data != null) {
            this.$message.success('新增成功')
            await this.reload()
          }
        } else {
          const res = await this.$axios.post('/api/recordModelSetting/update', payload)
          if (res?.data != null) {
            this.$message.success('已保存')
            await this.reload()
          }
        }
      } catch (e) {
        console.error(e)
        this.$message.error('保存失败')
      }
    },

    async saveAll() {
      const dirtyRows = this.rows.filter(r => r.dirty || r.isNew)
      if (!dirtyRows.length) {
        this.$message.info('没有需要保存的变更')
        return
      }
      // 校验
      for (const r of dirtyRows) {
        if (!r.questionType || !r.selectedId) {
          this.$message.warning('存在未填写完整的行，请先补全题型与模型')
          return
        }
      }
      try {
        this.savingAll = true
        await Promise.all(dirtyRows.map(async r => {
          const payload = {
            id: r.id || undefined,
            type: this.q.type || undefined,               // UPDATED
            fileId: this.q.fileId || undefined,
            configPackageId: this.q.configPackageId || undefined,
            testSetId: this.q.testSetId || undefined,     // NEW
            questionType: r.questionType,
            modelSettingId: r.selectedId
          }
          if (r.isNew) {
            await this.$axios.post('/api/recordModelSetting/add', payload)
          } else {
            await this.$axios.post('/api/recordModelSetting/update', payload)
          }
        }))
        this.$message.success('批量保存成功')
        await this.reload()
      } catch (e) {
        console.error(e)
        this.$message.error('批量保存失败')
      } finally {
        this.savingAll = false
      }
    },

    async removeRow(row, idx) {
      try {
        if (row.id) {
          await this.$axios.delete(`/api/recordModelSetting/${row.id}`)
          this.$message.success('已删除')
          await this.reload()
        } else {
          this.rows.splice(idx, 1)
        }
      } catch (e) {
        console.error(e)
        this.$message.error('删除失败')
      }
    },

    // ===== 详情 =====
    async openModelDetail(row) {
      const id = row?.selectedId || row?.modelSetting?.id || row?.modelSettingId
      if (!id) return
      try {
        this.modelDetailVisible = true
        if (row.modelSetting && row.modelSetting.id === id) {
          this.modelDetail = {...row.modelSetting}
          this.modelDetailLoading = false
        } else {
          this.modelDetailLoading = true
          const res = await this.$axios.get('/api/model-request/detail', {params: {id}})
          this.modelDetail = res?.data || {}
        }
      } catch (e) {
        console.error(e)
        this.$message.error('获取模型详情失败')
      } finally {
        this.modelDetailLoading = false
      }
    },

    // ===== 工具 =====
    renderModelLabel(opt) {
      if (!opt) return ''
      return `${opt.name}${opt.modelValue ? '（' + opt.modelValue + '）' : ''}${opt.disabled ? '【禁用】' : ''}`
    },
    parseJson(str) {
      try {
        return JSON.parse(str)
      } catch {
        return str || {}
      }
    },
    getResponseFormat(row) {
      if (row?.jsonobject) return 'JsonObject'
      if (row?.jsonschema) return 'JsonSchema'
      return 'Text'
    },
    fmtBool(v) {
      if (v === true) return '开'
      if (v === false) return '关'
      return '-'
    },
    fmtPrompt(v) {
      return v || '-'
    },
    isTwoRound(row) {
      return !!row?.enableNormalQsTwoRequest
    }
  }
}
</script>

<style scoped>
.toolbar {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.tip {
  margin-bottom: 0;
}

.actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.opt-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.opt-label {
  font-weight: 600;
}

.opt-sub {
  opacity: 0.6;
  font-size: 12px;
}

.model-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hint-line {
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.current {
  opacity: 0.8;
}
</style>
