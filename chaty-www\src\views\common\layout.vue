<template>
  <div class="container-wrapper">
    <el-container>
      <el-header class="no-padding">
        <nav-bar :menu-items="topBarMenuItems"></nav-bar>
      </el-header>
      <el-container style="height: calc(100vh - 62px);margin-top: 2px">
        <el-aside v-if="!getIsCollapse" style="width: 180px">
          <aside-menu @collapse="changeCollapse" :menu-items="asideMenuItems"></aside-menu>
        </el-aside>
        <el-main :class="{ 'no-padding': $route.name === 'classConfig' }" class="container-main">
          <router-view v-slot="{ Component }">
            <keep-alive>
              <component :is="Component" :key="$route.name" v-if="$route.meta.keepAlive"/>
            </keep-alive>
            <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive"/>
          </router-view>
        </el-main>
      </el-container>
    </el-container>

    <login-dialog/>
  </div>
</template>

<script>
import NavBar from '../../components/layout/nav-bar.vue'
import AsideMenu from '../../components/layout/aside-menu.vue'
import LoginDialog from '../login/login-dialog.vue'
import Footer from '../../components/layout/page-footer.vue'
import home from "@/views/home/<USER>";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store";

const store = useUserStore();
export default {
  computed: {
    ...mapState(useUserStore, ['getIsCollapse', 'getTempCollapse']),
    home() {
      return home
    }
  },
  mounted() {
    this.$axios.get(`/api/auth/getAuthData`).then(res => {
    })
    this.$refreshConfig();

    this.judegeCollapse(this.$route)
  },
  watch: {
    $route(to) {
      this.judegeCollapse(to)
    }
  },
  components: {
    NavBar,
    LoginDialog,
    Footer,
    AsideMenu
  },
  data() {
    return {
      topBarMenuItems: [
        {
          index: '/markPapers',
          label: '批改试卷',
          t: 'menu.markingExamPapers',
          icon: 'DocumentAdd',
          authority: 'markPapers'
        },
        {
          index: '/correctConfigPackages',
          label: '配标准卷',
          t: 'menu.processingSampleRolls',
          icon: 'EditPen',
          authority: 'correctConfigPackages'
        },
        {
          index: '/comprehensive',
          label: '留痕批改',
          t: "menu.comprehensiveCorrect",
          icon: 'Position',
          authority: 'comprehensive'
        },
      ],
      asideMenuItems: [
        {index: '/ftpMessage', label: "待办试卷", t: 'menu.ftpMessage', icon: 'bell', authority: 'ftpMessage'},
        {
          index: '/comprehensive',
          label: '留痕批改',
          t: "menu.comprehensiveCorrect",
          icon: 'tool',
          authority: 'comprehensive'
        },
        // { index: '/review', label: '作业批改' },
        {
          index: '/markPapers',
          label: '批改试卷',
          t: 'menu.markingExamPapers',
          icon: 'dashboard',
          authority: 'markPapers'
        },
        {
          index: '/correctConfigPackages',
          label: '配标准卷',
          t: 'menu.processingSampleRolls',
          icon: 'box',
          authority: 'correctConfigPackages'
        },
        {
          index: '/essayPapers',
          label: '作文批改(Debug)',
          t: "menu.essayCorrection",
          icon: 'chat-remove',
          authority: 'essayPapers'
        },
        {index: '/stats', label: "统计数据", t: 'menu.stats', icon: 'celluar', authority: 'stats'},
        // { index: '/docfile', label: '快速批改', t: 'menu.fastReview', icon: 'Edit', authority: 'docfile' },
        // { index: '/doccorrect', label: '试卷批改', t: 'menu.docReview', icon: 'Document', authority: 'doccorrect' },
        // {index: '/solve', label: '模型评估', t: "menu.modelEvaluation", icon: 'PieChart', authority: 'solve'},
        {index: '/class/config', label: '班级配置', t: "menu.classConfig", icon: 'checkbox', authority: 'classConfig'},
        {index: '/quickfix', label: "快速纠错", t: "menu.quickfix", icon: 'cursor', authority: 'quickFix'},
        {
          index: '/grading/correct',
          label: "结果评估",
          t: 'menu.gradingCorrect',
          icon: 'home-alt2',
          authority: 'gradingCorrect'
        },
        {index: '/survey/page', label: "预约问卷", t: 'menu.surveyPage', icon: 'next', authority: 'surveyPage'},
        {
          index: '/errorCorrectionStatistics/index',
          label: "纠错统计",
          t: 'menu.errorCorrectionStatistics',
          icon: 'alarm',
          authority: 'errorCorrectionStatistics'
        },
        {index: '/requestLog', label: "问答日志", t: 'menu.requestLog', icon: 'box', authority: 'requestLog'},
        {index: '/TestSetManage', label: "模型测试", t: 'menu.testSetManage', icon: 'box', authority: 'testSetManage'},
      ],
      isCollapse: false
    }
  },
  methods: {
    ...mapActions(useUserStore, ['setDefaultConfigs', 'changeIsCollapse', "setTempCollapse"]),
    changeCollapse(val) {
      this.isCollapse = val
    },
    judegeCollapse(to) {
      // const needTempCollapsePage = ["stepMarkPapers", "stepConfigPapers", "docconfig"];
      const needTempCollapsePage = [];
      if (needTempCollapsePage.includes(to.name) && !this.getIsCollapse) {
        this.changeIsCollapse()
        this.setTempCollapse(true)
      } else if (!needTempCollapsePage.includes(to.name)  && this.getIsCollapse) {
        this.changeIsCollapse()
        this.setTempCollapse(false);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-container {
  height: calc(100vh / 0.9 - 64px) !important;
}

.container-wrapper {
  width: 100%;
  background: #fff;
  padding: 0;
  height: calc(100vh / 0.9);

  .container-main {
    position: relative;
    width: 100%;
    height: calc(100vh / 0.9 - 64px) !important;

    &::before {
      content: "";
      background: url("/cat1.png") center no-repeat;
      background-size: 500px;
      opacity: 0.04;
      /* 将opacity设置为0到1之间的值来调整透明度 */
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      pointer-events: none;
    }
  }
}

.no-padding {
  padding: 0 !important;
}
</style>