<!-- ModelPaperDialog.vue -->
<template>
  <el-dialog
      v-model="visible"
      width="1200px"
      :close-on-click-modal="false"
      @close="onClose"
  >
    <template #title>
      <el-icon style="color: #faad14; margin-right: 6px; vertical-align: middle;">
        <InfoFilled />
      </el-icon>
      <span style="vertical-align: middle;">选择模型与试卷</span>
    </template>

    <div style="margin-bottom: 10px;">
      <span>说明：用于快速进行</span>
      <span style="color: #E6A23C; font-weight: bold;">模型测试</span>
      <br />
      <span>效果：【确认】该试卷已纠错，自动基于原卷和标准卷重新通过所选模型重新批改，并自动基于该试卷批改结果纠错，批改完成的试卷已纠错，在【详情】中查看模型批改结果</span>
    </div>

    <div class="form-content">
      <el-form>
        <el-form-item label="原试卷名称">
          <el-input v-model="form.basePaperName" disabled style="width: 300px" />
        </el-form-item>

        <el-table
            :data="form.tasks"
            border
            style="width: 100%"
            size="small"
            :row-class-name="rowClassName"
        >
          <el-table-column prop="newName" label="任务名称" align="center" min-width="220">
            <template #default="{ row }">
              <el-input v-model="row.newName" placeholder="请输入任务名称" />
            </template>
          </el-table-column>

          <!-- 触发“按题型选择模型”的弹窗 -->
          <el-table-column label="按题型选择模型" align="center" min-width="200">
            <template #default="{ $index }">
              <el-button type="primary" size="small" @click="openQtSelector($index)">
                按题型选择模型
              </el-button>
              <el-tag
                  v-if="form.tasks[$index]?.qtSelectedCount"
                  size="small"
                  style="margin-left: 8px;"
              >
                已选{{ form.tasks[$index].qtSelectedCount }}项
              </el-tag>
            </template>
          </el-table-column>

          <!-- 展示选择结果（题型-模型） -->
          <el-table-column label="已选模型" min-width="380" align="left">
            <template #default="{ row }">
              <div class="tag-wrap">
                <template v-if="row.qtSelectionResolved?.length">
                  <el-tag
                      v-for="it in row.qtSelectionResolved"
                      :key="it.questionType + '-' + it.modelSettingId"
                      effect="light"
                      type="success"
                      round
                      class="tag-item"
                  >
                    {{ it.questionType }}：{{ it.name || ('ID ' + it.modelSettingId) }}
                  </el-tag>
                </template>
                <span v-else class="tag-empty">未选择</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="标准卷" width="220" align="center">
            <template #default="{ row }">
              <el-switch v-model="row.needNewConfig" inactive-text="沿用" active-text="复制" />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" align="center">
            <template #default="{ $index }">
              <el-button
                  type="text"
                  @click="removeTask($index)"
                  v-if="form.tasks.length > 1"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-button type="primary" @click="addTask" style="margin-top: 10px;">
          添加重测任务
        </el-button>
      </el-form>
    </div>

    <!-- 表格外：单实例弹窗组件 -->
    <QuestionTypeModelSelector
        ref="qtSelector"
        :config-package-id="form.configPackageId"
        :hide-trigger="true"
        @confirmed="onQtConfirmed"
    />

    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="onConfirm" :loading="confirmLoading">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { useUserStore } from "@/store";
import QuestionTypeModelSelector from "@/views/QuestionType/QuestionTypeModelSelector.vue";
import { InfoFilled } from "@element-plus/icons-vue";

const store = useUserStore();

export default {
  name: "ModelPaperDialog",
  components: { QuestionTypeModelSelector, InfoFilled },
  data() {
    return {
      visible: false,
      form: {
        id: "",
        basePaperName: "",
        configPackageId: null, // 若有标准卷ID可在 show() 里传入
        tasks: [],
      },
      modelOptions: [],
      confirmLoading: false,
      datedBaseName: "",
      activeRowIndex: null,
      colorPalette: ["#e0ece0", "#f1f6f0", "#eef5fb", "#ECEFF1"],
    };
  },
  methods: {
    getTodayYYMMDD() {
      const now = new Date();
      const yy = String(now.getFullYear()).slice(-2);
      const mm = String(now.getMonth() + 1).padStart(2, "0");
      const dd = String(now.getDate()).padStart(2, "0");
      return yy + mm + dd;
    },
    computeDatedBaseName(rawName) {
      const dateStr = this.getTodayYYMMDD();
      const suffix = rawName.replace(/^\d{6}-?/, "");
      return `${dateStr}-${suffix}`;
    },
    show(defaultValues = {}) {
      this.$refreshConfig().then(() => {
        this.modelOptions = store.getAimodelOptions;
      });
      this.modelOptions = store.getAimodelOptions;

      const baseName = defaultValues.paperName || "";
      this.form.id = defaultValues.id || "";
      this.form.basePaperName = baseName;
      this.form.configPackageId = defaultValues.configPackageId ?? null;
      this.datedBaseName = this.computeDatedBaseName(baseName);

      // 初始化第一条任务，默认 v2，并加入题型选择占位字段
      this.form.tasks = [
        {
          modelValue: defaultValues.modelValue || null,
          needNewConfig: false,
          newName: `${this.datedBaseName}-v2`,
          qtSelectionMap: {},            // { [questionType]: modelSettingId }
          qtSelectionResolved: [],       // [{questionType, modelSettingId, name, modelValue}]
          qtSelectedCount: 0,
        },
      ];
      this.visible = true;
    },
    addTask() {
      const version = this.form.tasks.length + 2;
      this.form.tasks.push({
        modelValue: null,
        needNewConfig: false,
        newName: `${this.datedBaseName}-v${version}`,
        qtSelectionMap: {},
        qtSelectionResolved: [],
        qtSelectedCount: 0,
      });
    },
    removeTask(index) {
      this.form.tasks.splice(index, 1);
    },

    openQtSelector(idx) {
      this.activeRowIndex = idx;
      // 把当前行已选（若有）传给子组件，二次打开能回填
      const map = this.form.tasks[idx]?.qtSelectionMap || {};
      this.$refs.qtSelector.openDialog(map);
    },

    // 子组件确认回传：{ map, resolved }
    onQtConfirmed(payload) {
      const { map = {}, resolved = [] } = payload || {};
      const idx = this.activeRowIndex;
      if (idx == null) return;
      const task = this.form.tasks[idx];
      task.qtSelectionMap = map;
      task.qtSelectionResolved = resolved;
      task.qtSelectedCount = Object.keys(task.qtSelectionMap).length;
      this.$message.success(`第 ${idx + 1} 行已选 ${task.qtSelectedCount} 项`);
    },

    toModelSettings(map) {
      if (!map) return [];
      return Object.entries(map)
          .filter(([q, id]) => q && id)
          .map(([questionType, modelSettingId]) => ({ questionType, modelSettingId }));
    },

    async onConfirm() {
      const { id, tasks } = this.form;

      if (!id) {
        this.$message.error("缺少文件ID");
        return;
      }

      // 仅校验任务名（不再强制选择“模型”）
      for (let i = 0; i < tasks.length; i++) {
        const { newName } = tasks[i];
        if (!newName) {
          this.$message.error(`第${i + 1}项，请填写任务名称`);
          return;
        }
      }

      // 版本排序（数字大的排前）
      this.form.tasks.sort((a, b) => {
        const ma = a.newName.match(/-v(\d+)$/);
        const mb = b.newName.match(/-v(\d+)$/);
        if (ma && mb) {
          const na = parseInt(ma[1], 10);
          const nb = parseInt(mb[1], 10);
          if (na !== nb) return nb - na;
        }
        return a.newName.localeCompare(b.newName, undefined, {
          numeric: true,
          sensitivity: "base",
        });
      });

      const loading = this.$loading({
        lock: true,
        text: "加载中，请稍候",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      try {
        // 仅传 modelSettings，不再传 modelRequestId
        const body = this.form.tasks.map((task) => ({
          fileId: this.form.id,
          newName: task.newName,
          needNewConfig: task.needNewConfig,
          modelSettings: this.toModelSettings(task.qtSelectionMap),
        }));



        // 真正调用（调试完再开启）
        await this.$axios.post("/api/docCorrectFile/oneClickRetest", body);
        this.$message.success(`成功发起 ${tasks.length} 个重测任务`);
        this.onClose();
      } finally {
        loading.close();
      }
    },

    onClose() {
      this.visible = false;
      this.$emit("close");
    },

    rowClassName({ rowIndex }) {
      const group = Math.floor(rowIndex / 3);
      return `group-color-${group % 4}`;
    },
  },
};
</script>

<style scoped>
.form-content {
  font-size: 16px;
  line-height: 1.8;
}

.el-table {
  margin-bottom: 10px;
}

.tag-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  min-height: 28px;
}

.tag-item {
  margin: 2px 0;
}

.tag-empty {
  color: #909399;
  font-size: 12px;
}

:deep(.el-table__row.group-color-0) {
  background: #e0ece0 !important;
}

:deep(.el-table__row.group-color-1) {
  background: #f1f6f0 !important;
}

:deep(.el-table__row.group-color-2) {
  background: #eef5fb !important;
}

:deep(.el-table__row.group-color-3) {
  background: #eceff1 !important;
}
</style>
