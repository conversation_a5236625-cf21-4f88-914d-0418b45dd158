<template>
  <el-dialog
      title="Prompt 管理"
      v-model="visible"
      width="100vw"
      :before-close="onClose"
      :close-on-click-modal="false"
      custom-class="full-screen-dialog"
  >
    <!-- 新增：按类别与 中文名称 选择，然后新增一条“覆盖记录”（modelRequestId=record.id） -->
    <el-form inline class="add-form" style="margin-bottom: 16px;">
      <el-form-item label="选择类别">
        <el-select
            v-model="newType"
            placeholder="请选择类别"
            style="width: 220px"
            filterable
            clearable
            @change="onTypeChange"
        >
          <el-option
              v-for="type in types"
              :key="type"
              :label="type"
              :value="type"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择中文名称">
        <el-select
            v-model="newDefaultId"
            placeholder="请选择中文名称"
            style="width: 360px"
            :disabled="!newType"
            filterable
            clearable
        >
          <el-option
              v-for="optId in filteredDefaultIds"
              :key="optId"
              :label="defaultById[optId]?.name || '-'"
              :value="optId"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
            class="add-prompt-button"
            @click="onAdd"
            :disabled="!newDefaultId || addLoading"
            :loading="addLoading"
            style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
            @mouseenter="handleAddMouseEnter"
            @mouseleave="handleAddMouseLeave"
        >
          新增
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 类别筛选 -->
    <div class="filter-buttons" style="margin-bottom: 12px;">
      <el-button
        plain
        :class="listFilterType === '' ? 'all-button-active' : 'all-button-inactive'"
        @click="listFilterType = ''"
        :style="listFilterType === '' ? 'background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;' : ''"
        @mouseenter="listFilterType === '' ? handleAllMouseEnter : null"
        @mouseleave="listFilterType === '' ? handleAllMouseLeave : null">
        全部
      </el-button>
      <el-button
          v-for="type in types"
          :key="type"
          plain
          :type="listFilterType === type ? 'primary' : 'default'"
          @click="listFilterType = type"
      >
        {{ type }}
      </el-button>
    </div>

    <el-divider />

    <!-- 覆盖列表（当前 record.id 下的自定义提示词） -->
    <el-scrollbar style="height: 60vh;">
      <div
          v-for="item in filteredEntries"
          :key="item.id"
          class="prompt-item"
      >
        <div class="prompt-title">
          {{ item.name }}
          <el-tag
              v-if="isStringTag(item)"
              type="primary"
              style="margin-left: 8px;"
          >string</el-tag>
          <el-tag
              v-else-if="isJsonTag(item)"
              type="danger"
              style="margin-left: 8px;"
          >json</el-tag>
          <el-tag
              v-if="item.defaultMissing"
              type="warning"
              style="margin-left: 8px;"
          >无默认模板</el-tag>
        </div>

        <el-input
            type="textarea"
            v-model="item.value"
            :autosize="{ minRows: 4 }"
            class="prompt-input"
            placeholder="请输入提示词内容"
        />

        <div class="prompt-actions">
          <el-button
              class="update-prompt-button"
              @click="updatePrompt(item)"
              :loading="loadingMap[item.id]"
              style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
              @mouseenter="handleUpdateMouseEnter"
              @mouseleave="handleUpdateMouseLeave"
          >更改</el-button>
          <el-button
              type="warning"
              @click="resetPrompt(item)"
              :loading="loadingMap[item.id]"
              :disabled="item.defaultMissing"
              title="使用默认提示词覆盖当前内容"
          >重置为默认</el-button>
          <el-button
              type="danger"
              @click="deletePrompt(item)"
              :loading="loadingMap[item.id]"
          >删除</el-button>
        </div>

        <el-divider />
      </div>
    </el-scrollbar>

    <template #footer>
      <el-button @click="onClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'PromptManagerDialog',
  data() {
    return {
      visible: false,
      record: null, // 当前模型请求（包含 id）

      // 默认提示词（modelRequestId=null）
      defaultList: [],        // 数组：完整默认 Prompt 记录
      defaultById: {},        // id -> 默认记录

      // 当前模型请求下的覆盖列表
      promptEntries: [],      // 覆盖项数组
      takenUniKeys: new Set(),// 已覆盖的 uniKey 集合（用于“可新增中文名称”过滤）

      // UI 状态
      newType: '',
      newDefaultId: '',       // 选择的默认记录 id
      listFilterType: '',
      addLoading: false,
      loadingMap: {}
    };
  },
  computed: {
    // 类别集合（来自默认提示词）
    types() {
      return Array.from(
          new Set(this.defaultList.map(d => d.category || '未分类'))
      );
    },
    // 可新增的默认记录 id 集（按中文名排序），且未被覆盖
    filteredDefaultIds() {
      const filtered = this.defaultList
          .filter(d => (d.category || '未分类') === this.newType)
          .filter(d => !this.takenUniKeys.has(this.buildUniKey(d)));
      // 中文友好排序
      filtered.sort((a, b) => (a.name || '').localeCompare(b.name || '', 'zh-CN'));
      return filtered.map(d => d.id);
    },
    // 覆盖列表类别筛选
    filteredEntries() {
      return this.listFilterType
          ? this.promptEntries.filter(e => e.type === this.listFilterType)
          : this.promptEntries;
    }
  },
  methods: {
    show(rec) {
      if (!rec || !rec.id) {
        this.$message.error('缺少模型请求ID');
        return;
      }
      this.record = rec;
      this.visible = true;
      this.init();
    },
    onClose() {
      this.visible = false;
      this.resetState();
      this.$emit('refresh');
    },
    resetState() {
      this.record = null;
      this.defaultList = [];
      this.defaultById = {};
      this.promptEntries = [];
      this.takenUniKeys = new Set();
      this.newType = '';
      this.newDefaultId = '';
      this.listFilterType = '';
      this.loadingMap = {};
    },

    // 初始化：加载默认定义 + 当前模型请求覆盖
    async init() {
      try {
        await this.fetchDefaultDefs();
        await this.fetchOverrides();
      } catch (err) {
        console.error(err);
        this.$message.error('初始化失败');
      }
    },

    // 加载默认提示词定义（modelRequestId = null）
    async fetchDefaultDefs() {
      const dto = {
        modelRequestId: null,
        page: { pageNumber: 1, pageSize: 5000, searchCount: false }
      };
      const res = await this.$axios.post('/api/prompts/page', dto);
      const page = res.data?.data || res.data;
      const records = page?.records || [];

      this.defaultList = records.map(r => ({
        id: r.id,
        name: r.name || r.description || '',
        description: r.description || '',
        category: r.category || '未分类',
        isMajorType: !!r.isMajorType,
        questionType: r.questionType || '',
        majorStageType: r.majorStageType || '',
        role: r.role || '',
        hasImage: !!r.hasImage,
        tags: r.tags || 'string',
        promptContent: r.promptContent || ''
      }));

      const map = {};
      this.defaultList.forEach(d => { map[d.id] = d; });
      this.defaultById = map;
    },

    // 加载当前模型请求的覆盖（modelRequestId = record.id）
    async fetchOverrides() {
      const dto = {
        modelRequestId: this.record.id,
        page: {pageNumber: 1, pageSize: 5000, searchCount: false}
      };
      const res = await this.$axios.post('/api/prompts/page', dto);
      const page = res.data?.data || res.data;
      const records = page?.records || [];

      // 构建 takenUniKeys（与默认项匹配用）
      this.takenUniKeys = new Set(records.map(r => this.buildUniKey(r)));

      // 计算覆盖列表 + 是否能匹配到默认项
      this.promptEntries = records.map(r => {
        const match = this.findDefaultByUniKey(this.buildUniKey(r));
        return {
          id: r.id,
          name: r.name || r.description || '',
          type: r.category || '未分类',
          value: r.promptContent || '',
          raw: r,
          defaultMissing: !match,
          defaultId: match ? match.id : null
        };
      });
    },

    onTypeChange() {
      this.newDefaultId = '';
    },

    // 新增覆盖记录（以默认行为模板）
    async onAdd() {
      if (!this.newDefaultId) return;
      const def = this.defaultById[this.newDefaultId];
      if (!def) {
        this.$message.error('未找到默认模板');
        return;
      }
      this.addLoading = true;
      try {
        const payload = {
          // englishName 全部不传，后端也不需要
          description: def.description || '',
          name: def.name || '',
          isMajorType: def.isMajorType,
          questionType: def.questionType || '',
          category: def.category || '',
          promptContent: def.promptContent || '',
          majorStageType: def.majorStageType || '',
          tags: def.tags || 'string',
          role: def.role || '',
          hasImage: def.hasImage,
          modelRequestId: this.record.id
        };
        await this.$axios.post('/api/prompts/add', payload);
        this.$message.success('新增成功');
        await this.fetchOverrides();
        this.newDefaultId = '';
      } catch (e) {
        console.error(e);
        this.$message.error('新增失败');
      } finally {
        this.addLoading = false;
      }
    },

    // 更新覆盖记录：仅改内容；为防止空字段覆盖，把原始记录的字段一起回传
    async updatePrompt(item) {
      this.$set(this.loadingMap, item.id, true);
      try {
        const r = item.raw || {};
        const payload = {
          id: r.id,
          description: r.description,
          name: r.name,
          isMajorType: r.isMajorType,
          questionType: r.questionType,
          category: r.category,
          promptContent: item.value, // ★ 只变这个
          majorStageType: r.majorStageType,
          tags: r.tags || 'string',
          role: r.role,
          hasImage: r.hasImage,
          modelRequestId: r.modelRequestId
        };
        await this.$axios.post('/api/prompts/update', payload);
        this.$message.success('更改成功');
        await this.fetchOverrides();
      } catch (e) {
        console.error(e);
        this.$message.error('更改失败');
      } finally {
        this.$set(this.loadingMap, item.id, false);
      }
    },

    // 重置为默认：把默认提示词内容写回覆盖记录
    async resetPrompt(item) {
      if (item.defaultMissing) return;
      this.$set(this.loadingMap, item.id, true);
      try {
        const def = this.defaultById[item.defaultId];
        const r = item.raw || {};
        const payload = {
          id: r.id,
          description: r.description,
          name: r.name,
          isMajorType: r.isMajorType,
          questionType: r.questionType,
          category: r.category,
          promptContent: def?.promptContent || '',
          majorStageType: r.majorStageType,
          tags: r.tags || 'string',
          role: r.role,
          hasImage: r.hasImage,
          modelRequestId: r.modelRequestId
        };
        await this.$axios.post('/api/prompts/update', payload);
        this.$message.success('已重置为默认');
        await this.fetchOverrides();
      } catch (e) {
        console.error(e);
        this.$message.error('重置失败');
      } finally {
        this.$set(this.loadingMap, item.id, false);
      }
    },

    // 删除覆盖记录
    async deletePrompt(item) {
      this.$set(this.loadingMap, item.id, true);
      try {
        await this.$axios.post('/api/prompts/delete', null, {params: {id: item.id}});
        this.$message.success('删除成功');
        await this.fetchOverrides();
      } catch (e) {
        console.error(e);
        this.$message.error('删除失败');
      } finally {
        this.$set(this.loadingMap, item.id, false);
      }
    },

    /* ---------------- helpers ---------------- */
    // 用于默认项与覆盖项的稳定匹配
    buildUniKey(rec) {
      const name = (rec.name || rec.description || '').trim();
      const category = (rec.category || '').trim();
      const qt = (rec.questionType || '').trim();
      const stage = (rec.majorStageType || '').trim();
      const role = (rec.role || '').trim();
      const major = rec.isMajorType ? '1' : '0';
      const img = rec.hasImage ? '1' : '0';
      return `${name}#${category}#${qt}#${stage}#${role}#${major}#${img}`;
    },
    // 在默认列表中寻找与覆盖项相同的 uniKey
    findDefaultByUniKey(uniKey) {
      for (const d of this.defaultList) {
        if (this.buildUniKey(d) === uniKey) return d;
      }
      return null;
    },

    // UI 标记：string / json（根据中文名）
    isStringTag(item) {
      const text = (item.name || '').toString();
      return (
          text.includes('提示') ||
          text.includes('模板') ||
          item.type === '普通题目-大题类型' ||
          item.type === '标准卷批注'
      );
    },
    isJsonTag(item) {
      const text = (item.name || '').toString();
      return text.includes('格式');
    },
    handleAddMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleAddMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleAllMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleAllMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleUpdateMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleUpdateMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    }
  }
};
</script>

<style scoped>
.full-screen-dialog .el-dialog {
  width: 100vw !important;
  height: 100vh;
}

.full-screen-dialog .el-dialog__body,
.full-screen-dialog .el-dialog__footer {
  max-height: calc(100vh - 80px);
}

.prompt-item {
  margin-bottom: 16px;
}

.prompt-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.prompt-input {
  width: 100%;
}

.prompt-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.add-form {
  align-items: center;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
