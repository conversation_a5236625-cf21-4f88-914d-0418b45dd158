import chatDialog from "../views/chat/components/chat-dialog.vue";

export default {
    title: "喵喵汪汪",
    common: {
        logout: "退出登录",
        progress: "进度",
        error: "错误",
        save: "保存",
        review: "批改",
        ascOrder: "正序",
        descOrder: "反序",
        singlePage: "单页",
        reviewing: "批改中",
        delete: "删除",
        download: "下载",
        preview: "预览",
        document: "试卷",
        status: "状态",
        unReview: "未批改",
        reviewed: "已批改",
        reviewFail: "批改失败",
        correctRate: "正确率",
        operations: "操作",
        settings: "设置",
        fontSize: "字体大小",
        flagSize: "勾和叉的大小",
        type: "类型",
        upload: "上传",
        library: "知识库",
        confirm: "确认",
        cancel: "取消",
        saveSuccess: "保存成功",
        question: "题目",
        pleaseEnter: "请输入{0}",
        answer: "答案",
        evaluation: "评价",
        defaultEvaluation: "默认评价",
        search: "查询",
        select: "选择",
        emptyData: "无数据",
        deleteSuccess: "删除成功",
        clear: "清空",
        config: "配置",
        bigModel: "大模型",
        all: "全部",
        knowledge: "知识点",
        name: "名称",
        correctAnswer: "正确答案",
        isTrue: '是否正确',
        solve: "解题",
        true: "正确",
        downloadPdf: "下载PDF",
        creationTime: "创建时间",
        problem: "问题",
        tag: "标签",
        edit: "编辑",
        confirmDelete: "确认删除{0}?",
        updateSuccess: "更新成功",
        add: "添加",
        addSuccess: "添加成功",
        searchByKeyword: "请输入关键字搜索",
    },
    menu: {
        docReview: "试卷批改",
        solveProblem: "一键解题",
        coursenote: "讲义",
        library: "资料库",
        chat: "聊天",
        createDoc: "生成试卷",
        fastReview: "快速批改",
        modelEvaluation: "模型评估",
        markingExamPapers: "批改试卷",
        processingSampleRolls: "配标准卷",
        statistics: "统计",
        expand: "展开",
        collapse: "收起",
        languageSwitch: "语言切换",
        stats: "数据统计",
        essayCorrection: "作文批改",
        classConfig: '班级配置',
        comprehensiveCorrect: '留痕批改',
        quickfix: '快速纠错',
        gradingCorrect: "结果评估",
        surveyPage: "预约问卷",
        errorCorrectionStatistics: "纠错统计",
        requestLog: '问答日志',
        ftpMessage: '待办试卷',
        testSetManage: '模型测试',
    },
    docReview: {
        docConfig: "试卷配置",
        docAdd: "添加试卷",
        reviewRes: "批改结果",
        previewRes: "预览结果",
        docReport: "试卷报告",
        pleaseAddDoc: "请添加试卷",
        studentName: "学生姓名",
        addQuestion: "添加题目",
        save2Library: "保存到知识库",
        uploadFailed: "上传文档失败",
        docName: "试卷名称",
        updateDocName: "修改试卷名称",
        answerArea: "答题区域",
        evaluationArea: "评价区域",
        flagArea: "勾和叉的位置",
        reviewWithoutDoc: "批改不含原卷",
        reviewWithDoc: "批改不含原卷",
        statsRes: "统计结果",
        score: "分数",
    },
    solve: {
        promotPreview: "提示预览",
        solve: "解题",
        downloadPdf: "下载PDF",
        solveProblem: "一键解题",
        modelAnswer: "模型答案",
        finalAnswer: "最终答案",
        solveFailed: "解题失败",
        define: "定义",
        theorem: "定理",
        solveProcess: "解题过程",
        solveSuccess: "一键解题成功",
        pleaseCheckConfig: "请完善设置信息",
        pleaseSolve: "请解题",
        added2Library: "已添加到知识库",
    },
    coursenote: {
        docReview: "试卷批改",
        studentAnswer: "学生答案",
        modelList: "模型列表",
        outputRes: "输出结果",
        parseFailed: "解析失败",
        knowledgeName: "知识点名称",
        pleaseAddQuestion: "请添加题目",
    },
    library: {
        questionLibrary: "题库",
        homeworkReview: '作业批改',
    },
    docReviewRec: {
        docName: '批改试卷',
    },
    chat: {
        systemPromot: "系统消息",
        histiryMessageCount: "历史消息数量",
        historyMessageCompress: "历史消息压缩",
        historyMessageCompressThreshold: "历史消息压缩阈值",
        clearChat: "清空聊天",
    },
    login: {
        login: "登录",
        logoutSuccess: "退出登录成功",
        username: "用户名",
        password: "密码",
        loginSuccess: "登录成功",
    },
    home: {
        machine: "纸质作业留痕批改机器",
        tech: "大模型技术——AI全自动批改成为现实",
        reviewRate: "批改准确率",
        reviewRes: "批改效果",
        pointError: "指出具体错误",
        studentCheck: "学生诊断",
        personAnay: "个性化分析",
        docReview: "试卷批改",
        demoVideo: "演示视频",
    },
    printList: {
        default: "默认打印机",
        AIHardwareDemonstration: "AI+硬件演示",
        Teaching9223OnlyA4: "教九223仅a4"
    }
}