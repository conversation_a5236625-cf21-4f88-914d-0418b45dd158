<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchFormRef" :model="searchForm" class="header-form">
          <el-form-item label="测试集ID">
            <el-input
                v-model="searchForm.testSetId"
                placeholder="请输入测试集ID"
                clearable
                style="width: 200px;"
            />
          </el-form-item>

          <el-form-item label="图片ID">
            <el-input
                v-model="searchForm.imageId"
                placeholder="图片信息表ID"
                clearable
                style="width: 180px;"
            />
          </el-form-item>

          <el-form-item label="题型">
            <el-select
                v-model="searchForm.questionType"
                placeholder="全部"
                clearable
                filterable
                :loading="qtypeLoading"
                style="width: 180px;"
            >
              <el-option v-for="opt in questionTypeOptions" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>

          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 160px;">
              <el-option label="批改中" value="PROCESSING" />
              <el-option label="批改完成" value="DONE" />
            </el-select>
          </el-form-item>

          <el-form-item label="是否成功">
            <el-select v-model="searchForm.success" placeholder="全部" clearable style="width: 140px;">
              <el-option label="成功" :value="true" />
              <el-option label="失败" :value="false" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" :loading="loading" @click="loadData">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <!-- 表格 -->
    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          border
          empty-text="无数据"
      >
        <el-table-column type="index" label="序号" width="70" />

        <el-table-column label="图片" width="120">
          <template #default="{ row }">
            <el-image
                v-if="row.imgUrl"
                :src="row.imgUrl"
                :preview-src-list="[row.imgUrl]"
                fit="cover"
                style="width: 80px; height: 80px; border-radius: 6px;"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="imgUrl" label="图片地址" min-width="240">
          <template #default="{ row }">
            <a v-if="row.imgUrl" :href="row.imgUrl" target="_blank" class="simple-link">{{ row.imgUrl }}</a>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="题型" width="140">
          <template #default="{ row }">
            <el-tag v-if="row.questionType" size="small">{{ row.questionType }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="答题统计" width="220">
          <template #default="{ row }">
            <div>
              正确/总数：{{ row.correctCount ?? 0 }}/{{ row.totalCount ?? 0 }}
              <el-tag v-if="row.totalCount" size="small" type="success" style="margin-left: 6px;">
                {{ percent(row.correctCount, row.totalCount) }}
              </el-tag>
            </div>
            <div class="muted">是否成功：
              <el-tag :type="row.success ? 'success' : 'danger'" size="small">
                {{ row.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="row.status === 'DONE' ? 'success' : 'warning'">
              {{ row.status === 'DONE' ? '批改完成' : '批改中' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="模型参数" min-width="220">
          <template #default="{ row }">
            <div class="ellipsis">
              <span class="muted">名称：</span>{{ row.msName || '-' }}
            </div>
            <div class="ellipsis">
              <span class="muted">模型：</span>{{ row.msModelValue || '-' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="170" />
        <el-table-column prop="updateTime" label="更新时间" width="170" />

        <el-table-column label="操作" width="320" fixed="right" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="openFinalResult(row)">最终结果</el-link>
            <el-divider direction="vertical" />
            <el-link type="primary" @click="openImageSnapshot(row)">图片信息</el-link>
            <el-divider direction="vertical" />
            <el-link type="primary" @click="openModelSnapshot(row)">模型参数</el-link>
            <el-divider direction="vertical" />
            <el-link type="info" @click="openLogs(row)">批改日志</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- 最终结果 JSON 查看 -->
    <el-dialog title="最终批改结果 JSON" v-model="finalDialog.visible" width="760px" append-to-body>
      <pre class="json-pre">{{ finalDialog.pretty || '(空)' }}</pre>
      <template #footer>
        <el-button @click="finalDialog.visible=false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 图片信息快照查看 -->
    <el-dialog title="图片信息快照" v-model="imgDialog.visible" width="900px" append-to-body>
      <div class="img-snap-grid">
        <div>
          <div class="label">图片预览</div>
          <el-image
              v-if="imgDialog.data.imgUrl"
              :src="imgDialog.data.imgUrl"
              :preview-src-list="[imgDialog.data.imgUrl]"
              fit="contain"
              style="width: 240px; height: 240px; border-radius: 8px; background: #f5f7fa;"
          />
          <div v-else>-</div>
          <div class="mt8">
            <span class="muted">URL：</span>
            <a v-if="imgDialog.data.imgUrl" class="simple-link" :href="imgDialog.data.imgUrl" target="_blank">
              {{ imgDialog.data.imgUrl }}
            </a>
            <span v-else>-</span>
          </div>
        </div>
        <div>
          <div class="label">基础信息</div>
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="图片ID">{{ imgDialog.data.imageId ?? '-' }}</el-descriptions-item>
            <el-descriptions-item label="测试集ID">{{ imgDialog.data.testSetId ?? '-' }}</el-descriptions-item>
            <el-descriptions-item label="题型">{{ imgDialog.data.questionType ?? '-' }}</el-descriptions-item>
            <el-descriptions-item label="使用题面">
              <el-tag :type="imgDialog.data.useQuestionDetail ? 'success' : 'info'" size="small">
                {{ imgDialog.data.useQuestionDetail ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="来源试卷ID">{{ imgDialog.data.fromRecordId ?? '-' }}</el-descriptions-item>
            <el-descriptions-item label="来源区域号">{{ imgDialog.data.fromRecordAreaIdx ?? '-' }}</el-descriptions-item>
          </el-descriptions>

          <div class="label mt12">正确答案 JSON</div>
          <pre class="json-pre small">{{ pretty(imgDialog.data.rightAnswer) || '(空)' }}</pre>

          <div class="label mt12">题目信息 JSON</div>
          <pre class="json-pre small">{{ pretty(imgDialog.data.questionDetail) || '(空)' }}</pre>
        </div>
      </div>
      <template #footer>
        <el-button @click="imgDialog.visible=false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 模型参数快照查看 -->
    <el-dialog title="模型参数快照" v-model="msDialog.visible" width="900px" append-to-body>
      <el-descriptions :column="2" border size="small">
        <el-descriptions-item label="ID">{{ msDialog.data.modelSettingId ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="名称">{{ msDialog.data.msName ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="模型">{{ msDialog.data.msModelValue ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="权重">{{ msDialog.data.msWeight ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="禁用">
          <el-tag size="small" :type="boolType(msDialog.data.msDisabled)">{{ boolText(msDialog.data.msDisabled) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="题型">{{ msDialog.data.msQuestionType ?? '-' }}</el-descriptions-item>

        <el-descriptions-item label="JSON对象">
          <el-tag size="small" :type="boolType(msDialog.data.msJsonobject)">{{ boolText(msDialog.data.msJsonobject) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="JSON Schema">
          <el-tag size="small" :type="boolType(msDialog.data.msJsonschema)">{{ boolText(msDialog.data.msJsonschema) }}</el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="两轮询问">
          <el-tag size="small" :type="boolType(msDialog.data.msEnableNormalQsTwoRequest)">{{ boolText(msDialog.data.msEnableNormalQsTwoRequest) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="二轮用图">
          <el-tag size="small" :type="boolType(msDialog.data.msIsSecondRoundUseImage)">{{ boolText(msDialog.data.msIsSecondRoundUseImage) }}</el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="二轮JSON比对">
          <el-tag size="small" :type="boolType(msDialog.data.msIsSecondRoundJsonComparison)">{{ boolText(msDialog.data.msIsSecondRoundJsonComparison) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="增强图像">
          <el-tag size="small" :type="boolType(msDialog.data.msEnableImageEnhancement)">{{ boolText(msDialog.data.msEnableImageEnhancement) }}</el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="一轮提示词类型">{{ msDialog.data.msSingleRoundPromptType ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="R1 提示词类型">{{ msDialog.data.msFirstRoundPromptType ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="R2 提示词类型">{{ msDialog.data.msSecondRoundPromptType ?? '-' }}</el-descriptions-item>

        <el-descriptions-item label="创建时间">{{ msDialog.data.msCreateTime ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ msDialog.data.msUpdateTime ?? '-' }}</el-descriptions-item>
      </el-descriptions>

      <div class="label mt12">Prompt</div>
      <pre class="json-pre small">{{ msDialog.data.msPrompt || '(空)' }}</pre>

      <div class="label mt12">内容 Content</div>
      <pre class="json-pre small">{{ msDialog.data.msContent || '(空)' }}</pre>

      <div class="label mt12">备注 Remark</div>
      <pre class="json-pre small">{{ msDialog.data.msRemark || '(空)' }}</pre>

      <template #footer>
        <el-button @click="msDialog.visible=false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 批改日志 Dialog -->
    <el-dialog title="批改日志" v-model="logDialog.visible" width="860px" append-to-body>
      <div v-if="logDialog.loading" style="text-align:center; padding: 40px 0;">
        <el-icon class="is-loading"><i-ep-loading /></el-icon>
      </div>
      <template v-else>
        <el-descriptions :column="3" border size="small" class="mb12">
          <el-descriptions-item label="第一轮日志ID">{{ logDialog.meta.firstRoundLogId ?? '-' }}</el-descriptions-item>
          <el-descriptions-item label="第二轮日志ID">{{ logDialog.meta.secondRoundLogId ?? '-' }}</el-descriptions-item>
          <el-descriptions-item label="一轮日志ID">{{ logDialog.meta.singleRoundLogId ?? '-' }}</el-descriptions-item>
        </el-descriptions>

        <el-table
            v-if="Array.isArray(logDialog.data)"
            :data="logDialog.data"
            border
            size="small"
        >
          <el-table-column prop="time" label="时间" width="180" />
          <el-table-column prop="level" label="级别" width="100" />
          <el-table-column prop="message" label="内容" min-width="420">
            <template #default="{ row }">
              <pre class="log-pre">{{ row.message }}</pre>
            </template>
          </el-table-column>
        </el-table>

        <pre v-else class="log-pre">{{ logDialog.data || '（无日志）' }}</pre>
      </template>
      <template #footer>
        <el-button @click="logDialog.visible=false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TestSetImageResultManage',
  data() {
    return {
      loading: false,
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      searchForm: {
        testSetId: '',
        imageId: '',
        questionType: '',
        status: '',
        success: ''
      },
      questionTypeOptions: [],
      qtypeLoading: false,

      finalDialog: {
        visible: false,
        pretty: ''
      },
      imgDialog: {
        visible: false,
        data: {}
      },
      msDialog: {
        visible: false,
        data: {}
      },
      logDialog: {
        visible: false,
        loading: false,
        data: null,
        meta: {
          firstRoundLogId: null,
          secondRoundLogId: null,
          singleRoundLogId: null
        }
      }
    };
  },
  created() {
    // 支持从路由 query 预筛选，例如 ?testSetId=xxx&imageId=yyy
    const q = this.$route?.query || {};
    if (q.testSetId != null) this.searchForm.testSetId = String(q.testSetId);
    if (q.imageId != null) this.searchForm.imageId = String(q.imageId);
    if (q.questionType != null) this.searchForm.questionType = String(q.questionType);
    if (q.status != null) this.searchForm.status = String(q.status);
    if (q.success != null) this.searchForm.success = q.success === 'true' ? true : q.success === 'false' ? false : '';
    this.fetchQuestionTypes();
    this.loadData();
  },
  methods: {
    goBack() {
      this.$router && this.$router.back && this.$router.back();
    },
    // 获取题型选项
    async fetchQuestionTypes() {
      this.qtypeLoading = true;
      try {
        const res = await this.$axios.get('/api/prompts/questionTypes');
        const list = res?.data?.data ?? res?.data ?? [];
        this.questionTypeOptions = Array.isArray(list) ? list : [];
      } finally {
        this.qtypeLoading = false;
      }
    },
    // 百分比
    percent(ok, all) {
      const a = Number(ok || 0);
      const b = Number(all || 0);
      if (!b) return '0%';
      return ((a * 100) / b).toFixed(1) + '%';
    },
    // 列表
    async loadData() {
      this.loading = true;
      try {
        const params = {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
          testSetId: this.searchForm.testSetId || undefined,
          imageId: this.searchForm.imageId || undefined,
          questionType: this.searchForm.questionType || undefined,
          status: this.searchForm.status || undefined,
          success: this.searchForm.success === '' ? undefined : this.searchForm.success
        };
        const res = await this.$axios.get('/api/testsetImageResult/page', { params });
        const pageObj = res?.data?.data ?? res?.data ?? {};
        this.tableData = pageObj.records || [];
        this.total = pageObj.total || 0;
        this.pageSize = pageObj.size || this.pageSize;
        this.pageNumber = pageObj.current || this.pageNumber;
      } finally {
        this.loading = false;
      }
    },
    reset() {
      this.searchForm = { testSetId: '', imageId: '', questionType: '', status: '', success: '' };
      this.pageNumber = 1;
      this.loadData();
    },
    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },
    // JSON pretty
    pretty(obj) {
      try {
        if (obj == null) return '';
        if (typeof obj === 'string') return JSON.stringify(JSON.parse(obj), null, 2);
        return JSON.stringify(obj, null, 2);
      } catch {
        return String(obj);
      }
    },
    // 查看最终结果
    openFinalResult(row) {
      this.finalDialog.pretty = this.pretty(row.finalResult) || '(空)';
      this.finalDialog.visible = true;
    },
    // 查看图片快照
    openImageSnapshot(row) {
      this.imgDialog.data = {
        imageId: row.imageId,
        testSetId: row.testSetId,
        imgUrl: row.imgUrl,
        questionType: row.questionType,
        useQuestionDetail: row.useQuestionDetail,
        fromRecordId: row.fromRecordId,
        fromRecordAreaIdx: row.fromRecordAreaIdx,
        rightAnswer: row.rightAnswer,
        questionDetail: row.questionDetail
      };
      this.imgDialog.visible = true;
    },
    // 查看模型快照
    openModelSnapshot(row) {
      this.msDialog.data = { ...row };
      this.msDialog.visible = true;
    },
    // 批改日志
    async openLogs(row) {
      this.logDialog.visible = true;
      this.logDialog.loading = true;
      this.logDialog.meta = {
        firstRoundLogId: row.firstRoundLogId,
        secondRoundLogId: row.secondRoundLogId,
        singleRoundLogId: row.singleRoundLogId
      };
      try {
        // 优先 /api/testsetImageResult/logs?resultId=xxx
        let data = null;
        try {
          const res = await this.$axios.get('/api/testsetImageResult/logs', { params: { resultId: row.id } });
          data = res?.data?.data ?? res?.data ?? null;
        } catch (e) {
          // 兼容 /api/testsetImageResult/{id}/logs
          const res2 = await this.$axios.get(`/api/testsetImageResult/${row.id}/logs`);
          data = res2?.data?.data ?? res2?.data ?? null;
        }
        this.logDialog.data = data;
      } catch {
        this.logDialog.data = '（拉取日志失败）';
      } finally {
        this.logDialog.loading = false;
      }
    },
    // 删除
    async onDelete(id) {
      await this.$confirm('确认删除该记录？', '警告', { type: 'warning' });
      await this.$axios.delete(`/api/testsetImageResult/${id}`);
      this.$message.success('删除成功');
      this.loadData();
    },
    boolType(v) {
      if (v === true) return 'success';
      if (v === false) return 'info';
      return 'info';
    },
    boolText(v) {
      if (v === true) return '是';
      if (v === false) return '否';
      return '-';
    }
  }
};
</script>

<style scoped lang="scss">
.simple-link {
  color: #1890ff;
  text-decoration: underline;
}
.simple-link:hover { color: #40a9ff; }

.muted { color: var(--el-text-color-secondary); }
.mb12 { margin-bottom: 12px; }
.mt8 { margin-top: 8px; }
.mt12 { margin-top: 12px; }

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
    background: #fff;
    padding: 12px;
    border-radius: 8px;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.json-pre {
  background: #0f172a;
  color: #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  max-height: 420px;
  overflow: auto;
  font-size: 12px;
  line-height: 1.5;
}
.json-pre.small { max-height: 260px; }

.img-snap-grid {
  display: grid;
  grid-template-columns: 280px 1fr;
  grid-gap: 16px;
  align-items: start;
}
.label {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-bottom: 6px;
}
.log-pre {
  background: #0f172a;
  color: #e2e8f0;
  border-radius: 6px;
  padding: 10px;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
