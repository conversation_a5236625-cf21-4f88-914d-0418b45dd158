<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchForm" :model="searchForm" class="header-form">
          <el-form-item label="名称">
            <el-input v-model="searchForm.name" placeholder="请输入名称" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="searchForm.remark" placeholder="请输入备注" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item>
            <el-button
              class="query-button"
              :loading="loading"
              @click="loadData"
              style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important; text-shadow: 0.3px 0 0 currentColor, -0.3px 0 0 currentColor !important;"
              @mouseenter="handleQueryMouseEnter"
              @mouseleave="handleQueryMouseLeave">
              查询
            </el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="success" @click="goCreate()">新增</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <!-- 表格 -->
    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          empty-text="无数据"
          :row-style="getRowStyle"
      >
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="name" label="名称(不可重复)" width="240" />

        <!-- 模型值保留单独一列 -->
        <el-table-column prop="modelValue" label="模型值" width="220">
          <template #default="{ row }">
            <span class="mono">{{ row.modelValue || '-' }}</span>
          </template>
        </el-table-column>

        <!-- 合并参数：以标签形式友好展示 -->
        <el-table-column label="其他参数" min-width="520">
          <template #default="{ row }">
            <el-space wrap size="small" alignment="center" class="tag-wrap">
              <!-- 响应格式 -->
              <el-tag v-if="getResponseFormat(row) !== '-'" size="small" type="info" effect="plain">
                {{ getResponseFormat(row) }}
              </el-tag>

              <!-- 题型（如果存在） -->
              <el-tag v-if="row.questionType" size="small" type="success" effect="plain">
                题型：{{ row.questionType }}
              </el-tag>

              <!-- 单/双轮 & 各轮提示词 -->
              <el-tag v-if="isBool(row.enableNormalQsTwoRequest)" :type="row.enableNormalQsTwoRequest ? 'success' : 'info'" size="small" effect="plain">
                {{ row.enableNormalQsTwoRequest ? '两轮' : '单轮' }}
              </el-tag>

              <el-tag
                  v-if="!row.enableNormalQsTwoRequest && row.singleRoundPromptType"
                  size="small" type="primary" effect="plain"
              >
                一轮：{{ row.singleRoundPromptType }}
              </el-tag>

              <el-tag
                  v-if="row.enableNormalQsTwoRequest && row.firstRoundPromptType"
                  size="small" type="primary" effect="plain"
              >
                R1：{{ row.firstRoundPromptType }}
              </el-tag>

              <el-tag
                  v-if="row.enableNormalQsTwoRequest && row.secondRoundPromptType && !row.isSecondRoundJsonComparison"
                  size="small" type="primary" effect="plain"
              >
                R2：{{ row.secondRoundPromptType }}
              </el-tag>

              <!-- 第二轮衍生开关 -->
              <el-tag
                  v-if="row.enableNormalQsTwoRequest && isBool(row.isSecondRoundUseImage)"
                  size="small" :type="row.isSecondRoundUseImage ? 'warning' : 'info'" effect="plain"
              >
                二轮用图
              </el-tag>

              <el-tag
                  v-if="row.enableNormalQsTwoRequest && isBool(row.isSecondRoundJsonComparison)"
                  size="small" :type="row.isSecondRoundJsonComparison ? 'warning' : 'info'" effect="plain"
              >
                二轮JSON比对
              </el-tag>

              <!-- 图像增强 -->
              <el-tag
                  v-if="isBool(row.enableImageEnhancement)"
                  size="small" :type="row.enableImageEnhancement ? 'success' : 'info'" effect="plain"
              >
                增强图像
              </el-tag>

              <!-- 禁止批改 -->
              <el-tag
                  v-if="isBool(row.disabled)"
                  size="small" :type="row.disabled ? 'danger' : 'success'" effect="plain"
              >
                {{ row.disabled ? '禁止批改' : '允许批改' }}
              </el-tag>

              <!-- 参数 JSON 快捷查看 -->
              <el-tag
                  v-if="row.content"
                  size="small"
                  type="info"
                  effect="light"
                  class="clickable"
                  @click.stop="showJson(row.content)"
              >
                参数JSON
              </el-tag>

              <!-- JsonObject / JsonSchema 原始标识（兼容部分后端字段独立使用场景） -->
              <el-tag v-if="row.jsonobject && !row.jsonschema" size="small" type="info" effect="plain">JSON对象</el-tag>
              <el-tag v-if="row.jsonschema" size="small" type="info" effect="plain">JSON Schema</el-tag>
            </el-space>
          </template>
        </el-table-column>

        <el-table-column prop="weight" label="排序权重" width="110">
          <template #default="{ row }">
            <el-tag size="small" type="info" effect="plain" class="mono">{{ row.weight ?? '-' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="remark" label="备注" width="180">
          <template #default="{ row }">
            <el-tooltip class="item" effect="dark" :content="row.remark || ''" placement="top">
              <span>
                {{ (row.remark || '').length > 28 ? (row.remark || '').slice(0, 28) + '...' : (row.remark || '-') }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="200" />

        <el-table-column label="操作" fixed="right" width="220">
          <template #default="{ row }">
            <el-link type="primary" @click="goEdit(row)">编辑</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
            <el-divider direction="vertical" />
            <el-link type="success" @click="copyRecord(row)">复制</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- JSON 查看对话框 -->
    <el-dialog title="查看 JSON 数据" v-model="jsonDialogVisible" width="800px">
      <json-viewer :value="currentJson" />
      <el-button
        icon="CopyDocument"
        class="copy-json-button"
        @click="copyJson"
        style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
        @mouseenter="handleCopyJsonMouseEnter"
        @mouseleave="handleCopyJsonMouseLeave">
        复制
      </el-button>
      <template #footer>
        <el-button @click="jsonDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { JsonViewer } from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'

const EDIT_CACHE_KEY = 'model-request:edit-cache' // sessionStorage key

export default {
  name: 'ModelRequestManager',
  components: { JsonViewer },
  data() {
    return {
      searchForm: { name: '', remark: '' },
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      // JSON 查看
      jsonDialogVisible: false,
      currentJson: null,

      maxEmptyWight: 0,
      default: null,
      colorPalette: ['#e0ece0', '#f1f6f0', '#eef5fb', '#ECEFF1'],
      nameColorMap: {},
      colorPaletteIdx: 0
    }
  },
  methods: {
    // —— 跳转编辑/新增 —— //
    goCreate() {
      sessionStorage.setItem(EDIT_CACHE_KEY, JSON.stringify(null))
      this.$router.push({ name: 'ModelRequestEdit' })
    },
    goEdit(row) {
      // 将当前行缓存，以便编辑页可直接渲染
      sessionStorage.setItem(EDIT_CACHE_KEY, JSON.stringify(row || null))
      this.$router.push({ name: 'ModelRequestEdit', query: { id: row?.id } })
    },

    copyRecord(row) {
      const now = new Date()
      const pad = n => n.toString().padStart(2, '0')
      const ts = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}`
      const newName = `${row.name}_${ts}`
      const payload = { ...row, id: null, name: newName }
      payload.jsonobject = row.jsonobject
      payload.jsonschema = row.jsonschema
      payload.disabled = !!row.disabled

      this.$confirm(`确定复制"${row.name}"吗？`, '复制确认', { type: 'warning' })
          .then(() => this.$axios.post('/api/model-request/add', payload))
          .then(() => { this.$message.success('复制并新增成功'); this.loadData() })
          .catch(() => {})
    },
    async copyJson() {
      const text = typeof this.currentJson === 'string' ? this.currentJson : JSON.stringify(this.currentJson, null, 2)
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('JSON 已复制到剪贴板')
      } catch (e) {
        const textarea = document.createElement('textarea')
        textarea.value = text
        document.body.appendChild(textarea)
        textarea.select()
        try { document.execCommand('copy'); this.$message.success('JSON 已复制到剪贴板') }
        catch { this.$message.error('复制失败，请手动复制') }
        document.body.removeChild(textarea)
      }
    },
    isBool(v) { return typeof v === 'boolean' },

    calculateNameColorMap(data) {
      const freq = {}
      data.forEach(row => {
        let groupKey = ''
        if (row.name) {
          const match = String(row.name).match(/^\d{6}/)
          groupKey = match ? match[0] : ''
        }
        if (groupKey) freq[groupKey] = (freq[groupKey] || 0) + 1
      })
      const map = {}
      let idx = 0
      const colorPalette = this.colorPalette
      data.forEach(row => {
        let groupKey = ''
        if (row.name) {
          const match = String(row.name).match(/^\d{6}/)
          groupKey = match ? match[0] : ''
        }
        if (groupKey && freq[groupKey] > 1 && !map[groupKey]) {
          map[groupKey] = colorPalette[idx % colorPalette.length]
          idx++
        }
      })
      this.nameColorMap = map
      this.colorPaletteIdx = idx
    },
    getRowStyle({ row }) {
      if (this.default && this.default.id && row.id === this.default.id) return { background: '#ffe8e8' }
      if (row.disabled) return { background: '#f6f6f6', color: '#999' }
      let groupKey = ''
      if (row.name) {
        const match = String(row.name).match(/^\d{6}/)
        groupKey = match ? match[0] : ''
      }
      const bg = this.nameColorMap[groupKey]
      return bg ? { background: bg } : {}
    },
    goBack() { this.$router.back() },
    loadData() {
      this.loading = true
      this.$axios.post('/api/model-request/selectPage', {
        page: { pageNumber: this.pageNumber, pageSize: this.pageSize, searchCount: true },
        name: this.searchForm.name,
        remark: this.searchForm.remark
      }).then(res => {
        const d = res.data
        this.calculateNameColorMap(d.records || [])
        this.tableData = d.records || []
        this.total = d.total || 0
      }).finally(() => { this.loading = false })

      this.$axios.get('/api/model-request/getMaxEmptyWeight').then(res => { this.maxEmptyWight = res.data })
      this.$axios.get('/api/model-request/default/get').then(res => { this.default = res.data })
      this.$refreshConfig()
    },
    reset() {
      this.searchForm = { name: '', remark: '' }
      this.pageNumber = 1
      this.loadData()
    },
    handlePageChange(page) { this.pageNumber = page; this.loadData() },
    onDelete(id) {
      this.$confirm('确认删除该记录？', '警告', { type: 'warning' })
          .then(() => this.$axios.get(`/api/model-request/delete?id=${id}`))
          .then(() => { this.$message.success('删除成功'); this.loadData() })
          .catch(() => {})
    },
    showJson(jsonStr) {
      try { this.currentJson = JSON.parse(jsonStr) }
      catch { this.currentJson = jsonStr; this.$message.warning('无法解析为 JSON') }
      this.jsonDialogVisible = true
    },
    getResponseFormat(row) {
      if (row.disabled) return '-'
      if (row.jsonobject) return 'JsonObject'
      if (row.jsonschema) return 'JsonSchema'
      return 'Text'
    },
    handleQueryMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleQueryMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleCopyJsonMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleCopyJsonMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    }
  },
  created() {
    this.loadData()
  },
  beforeUnmount() {
    this.$refreshConfig()
  }
}
</script>

<style lang="scss" scoped>
:deep(.highlight-default-row) {
  background-color: #ffe8e8 !important;
}

.simple-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.simple-link:hover {
  color: #40a9ff;
}

.mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.tag-wrap :deep(.el-tag) {
  border-radius: 8px;
}

.clickable {
  cursor: pointer;
}

.clickable:hover {
  opacity: 0.85;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

/* 分组底色（保留与你原逻辑一致） */
:deep(.row-bg-) {
  background-color: inherit;
}

:deep(.row-bg-doubao) {
  background-color: #e0ece0 !important;
}

:deep(.row-bg-doubas) {
  background-color: #f1f6f0 !important;
}

:deep(.row-bg-douba1) {
  background-color: #eef5fb !important;
}

:deep(.row-bg-douba2) {
  background-color: #ECEFF1 !important;
}
</style>
