<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchForm" :model="searchForm" class="header-form">
          <el-form-item label="名称">
            <el-input
                v-model="searchForm.name"
                placeholder="请输入题型名称"
                clearable
                style="width: 220px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              class="query-button"
              :loading="loading"
              @click="loadData"
              style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important; text-shadow: 0.3px 0 0 currentColor, -0.3px 0 0 currentColor !important;"
              @mouseenter="handleQueryMouseEnter"
              @mouseleave="handleQueryMouseLeave">
              查询
            </el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="success" @click="openDialog()">新增</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <!-- 表格 -->
    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          empty-text="无数据"
      >
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="name" label="题型名称" width="240" />
        <el-table-column prop="description" label="描述">
          <template #default="{ row }">
            <el-tooltip effect="dark" :content="row.description || ''" placement="top">
              <span>
                {{ (row.description || '').length > 40 ? (row.description || '').slice(0, 40) + '...' : (row.description || '-') }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>

        <!-- 默认模型（直接使用 list 返回的 modelSetting） -->
        <el-table-column label="默认模型" width="220">
          <template #default="{ row }">
            <span v-if="!row.defaultModelRequestId">-</span>
            <span
                v-else
                class="simple-link"
                @click="openModel(row.modelSetting)"
            >
              {{ row.modelSetting?.name || `ID#${row.defaultModelRequestId}` }}
            </span>
          </template>
        </el-table-column>

        <!-- 新增列 1：模型值 -->
        <el-table-column label="模型值" width="240">
          <template #default="{ row }">
            <span v-if="row.modelSetting?.modelValue">{{ row.modelSetting.modelValue }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 新增列 2：开启两轮询问 -->
        <el-table-column label="开启两轮询问" width="140">
          <template #default="{ row }">
            <template v-if="isBool(row.modelSetting?.enableNormalQsTwoRequest)">
              <el-tag :type="row.modelSetting.enableNormalQsTwoRequest ? 'success' : 'info'" size="small">
                {{ row.modelSetting.enableNormalQsTwoRequest ? '已开启' : '已关闭' }}
              </el-tag>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 新增列 3：其他参数 -->
        <el-table-column label="其他参数" min-width="420">
          <template #default="{ row }">
            <template v-if="row.modelSetting">
              <el-space wrap>
                <el-tag v-if="row.modelSetting.jsonobject" size="small" type="info">JSON对象</el-tag>
                <el-tag v-if="row.modelSetting.jsonschema" size="small" type="info">JSON Schema</el-tag>

                <el-tag v-if="row.modelSetting.isSecondRoundUseImage" size="small" type="warning">二轮用图</el-tag>
                <el-tag v-if="row.modelSetting.isSecondRoundJsonComparison" size="small" type="warning">二轮JSON比对</el-tag>

                <el-tag v-if="row.modelSetting.enableImageEnhancement" size="small" type="success">增强图像</el-tag>

                <el-tag
                    v-if="row.modelSetting.singleRoundPromptType && row.modelSetting.enableNormalQsTwoRequest === false"
                    size="small"
                    type="primary"
                >
                  一轮：{{ row.modelSetting.singleRoundPromptType }}
                </el-tag>
                <el-tag
                    v-if="row.modelSetting.firstRoundPromptType && row.modelSetting.enableNormalQsTwoRequest === true"
                    size="small"
                    type="primary"
                >
                  R1：{{ row.modelSetting.firstRoundPromptType }}
                </el-tag>
                <el-tag
                    v-if="row.modelSetting.secondRoundPromptType && row.modelSetting.enableNormalQsTwoRequest === true"
                    size="small"
                    type="primary"
                >
                  R2：{{ row.modelSetting.secondRoundPromptType }}
                </el-tag>
<!--                <el-tag-->
<!--                    v-if="row.modelSetting.questionType"-->
<!--                    size="small"-->
<!--                    type="success"-->
<!--                >-->
<!--                  题型：{{ row.modelSetting.questionType }}-->
<!--                </el-tag>-->
              </el-space>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 新增列 4：禁止批改（disabled） -->
        <el-table-column label="禁止批改" width="120">
          <template #default="{ row }">
            <template v-if="isBool(row.modelSetting?.disabled)">
              <el-tag :type="row.modelSetting.disabled ? 'danger' : 'success'" size="small">
                {{ row.modelSetting.disabled ? '是' : '否' }}
              </el-tag>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="200" />
        <el-table-column prop="updateTime" label="更新时间" width="200" />

        <el-table-column label="操作" fixed="right" width="220">
          <template #default="{ row }">
            <el-link type="primary" @click="openDialog(row)">编辑</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="isEdit ? '编辑题型' : '新增题型'" v-model="dialogVisible" width="700px">
      <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogRules" label-width="140px">
        <el-form-item label="题型名称" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入题型名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dialogForm.description" placeholder="请输入描述" />
        </el-form-item>

        <el-divider content-position="left">默认模型绑定</el-divider>
        <el-form-item label="默认模型">
          <div style="display:flex; gap:8px; width:100%;">
            <el-autocomplete
                v-model="dialogForm.defaultModelLabel"
                :fetch-suggestions="fetchModelSuggestions"
                placeholder="输入模型名称关键字搜索"
                style="flex:1;"
                @select="onModelSelected"
                clearable
                @clear="clearSelectedModel"
            />
            <el-button @click="clearSelectedModel">清除绑定</el-button>
          </div>
          <div v-if="dialogForm.defaultModelRequestId" class="desc-id">
            当前绑定ID：{{ dialogForm.defaultModelRequestId }}
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          class="confirm-button"
          :loading="dialogLoading"
          @click="submitDialog"
          style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
          @mouseenter="handleConfirmMouseEnter"
          @mouseleave="handleConfirmMouseLeave">
          确定
        </el-button>
      </template>
    </el-dialog>

    <model-request-detail-dialog ref="modelRequestDetailDialog"></model-request-detail-dialog>
  </div>
</template>

<script>
import modelRequestDetailDialog from '@/views/ModelRequest/modelRequestDetailDialog.vue'
export default {
  components: { modelRequestDetailDialog },
  name: 'QuestionTypeManager',
  data() {
    return {
      searchForm: { name: '' },
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      loading: false,

      dialogVisible: false,
      isEdit: false,
      dialogLoading: false,
      dialogFormRef: null,
      dialogForm: {
        id: null,
        name: '',
        description: '',
        defaultModelRequestId: null,
        defaultModelLabel: '' // 仅用于展示/搜索
      },
      dialogRules: {
        name: [{ required: true, message: '题型名称不能为空', trigger: 'blur' }]
      }
    };
  },
  methods: {
    goBack() {
      this.$router.back();
    },
    isBool(v) {
      return typeof v === 'boolean';
    },

    // 加载列表（直接使用后端返回的 modelSetting）
    loadData() {
      this.loading = true;
      this.$axios.post('/api/questionType/list', null, {
        params: {
          name: this.searchForm.name || '',
          pageNo: this.pageNumber,
          pageSize: this.pageSize
        }
      }).then(res => {
        const d = res?.data || {};
        this.tableData = d.records || [];
        this.total = d.total || 0;
      }).finally(() => {
        this.loading = false;
      });
    },

    reset() {
      this.searchForm = {name: ''};
      this.pageNumber = 1;
      this.loadData();
    },

    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },

    // 打开新增/编辑（默认模型名直接来自 row.modelSetting）
    openDialog(row = null) {
      if (row) {
        this.isEdit = true;
        this.dialogForm = {
          id: row.id,
          name: row.name || '',
          description: row.description || '',
          defaultModelRequestId: row.defaultModelRequestId || null,
          defaultModelLabel: row.modelSetting?.name || ''
        };
      } else {
        this.isEdit = false;
        this.dialogForm = {
          id: null,
          name: '',
          description: '',
          defaultModelRequestId: null,
          defaultModelLabel: ''
        };
      }
      this.dialogVisible = true;
    },

    clearSelectedModel() {
      this.dialogForm.defaultModelRequestId = null;
      this.dialogForm.defaultModelLabel = '';
    },

    // 远程搜索模型（保持不变）
    fetchModelSuggestions(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      this.$axios.post('/api/model-request/selectPage', {
        page: {pageNumber: 1, pageSize: 20, searchCount: false},
        name: queryString,
        remark: ''
      }).then(res => {
        const list = res?.data?.records || [];
        const items = list.map(r => ({
          value: r.name,
          id: r.id,
          name: r.name,
          modelValue: r.modelValue
        }));
        cb(items);
      }).catch(() => cb([]));
    },

    onModelSelected(item) {
      this.dialogForm.defaultModelRequestId = item.id;
      this.dialogForm.defaultModelLabel = item.name || item.value || '';
    },

    openModel(id) {
      this.$refs.modelRequestDetailDialog.show(id);
    },

    submitDialog() {
      this.$refs.dialogFormRef.validate(valid => {
        if (!valid) return;
        this.dialogLoading = true;
        const api = this.isEdit ? '/api/questionType/update' : '/api/questionType/add';
        const payload = {
          id: this.dialogForm.id,
          name: this.dialogForm.name,
          description: this.dialogForm.description,
          defaultModelRequestId: this.dialogForm.defaultModelRequestId
        };
        this.$axios.post(api, payload)
            .then(() => {
              this.$message.success(this.isEdit ? '更新成功' : '新增成功');
              this.dialogVisible = false;
              this.loadData();
            })
            .finally(() => {
              this.dialogLoading = false;
            });
      });
    },

    onDelete(id) {
      this.$confirm('确认删除该题型？', '警告', {type: 'warning'})
          .then(() => this.$axios.get('/api/questionType/delete', {params: {id}}))
          .then(() => {
            this.$message.success('删除成功');
            this.loadData();
          })
          .catch(() => {
          });
    },
    handleQueryMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleQueryMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleConfirmMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleConfirmMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    }
  },
  created() {
    this.loadData();
  }
};
</script>

<style lang="scss" scoped>
.simple-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
}

.simple-link:hover {
  color: #40a9ff;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
