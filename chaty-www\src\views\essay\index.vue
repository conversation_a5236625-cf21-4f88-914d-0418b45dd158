<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-image src="/icon/16.png" class="left-icon"></el-image>
          <el-text class="title">作文批改</el-text>
          <el-input class="filter-item" v-model="essayPapersIndexPageData.filter.name" placeholder="请输入名称"/>
          <el-select class="filter-item" v-model="essayPapersIndexPageData.filter.status" placeholder="请选择状态"
                     clearable>
            <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
          <el-button class="search-button" @click="loadData">搜索</el-button>
          <el-button linked @click="reset" style="margin-left: 10px">重置</el-button>
          <el-button linked @click="manualRefresh" style="margin-left: 10px; display: flex; align-items: center;">
            <el-icon style="margin-right: 2px;">
              <RefreshRight/>
            </el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <template #extra>
        <div style="display: flex;align-items: center">
          <el-statistic class="header-action header-stats" title="批改速度" :value="statusStats.rpm"/>
          <el-statistic class="header-action header-stats" title="试卷数量" value-style="color: #E6A23C;"
                        :value="statusStats.total"/>
          <el-statistic class="header-action header-stats" title="批改中" value-style="color: #67C23A;"
                        :value="statusStats[2]"/>
          <el-statistic class="header-action header-stats" title="已批改" value-style="color: #E74C3C;"
                        :value="statusStats[3]"/>
        </div>

      </template>
    </el-page-header>

    <div class="start-config">
      <div class="start-button" type="primary" icon="EditPen" @click="$router.push('/essayPapers/stepEssayPapers/0')">
        <el-image src="/icon/essayStart.svg" class="icon"></el-image>
        <el-text class="text">创建作文任务</el-text>
      </div>
    </div>

    <div class="main-content">
      <el-table v-loading="taskLoading" :data="tasks" style="height: 100%" empty-text="无数据" :border="false">
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column"
                         :align="column?.align ?? 'center'"
                         :fixed="column.prop === 'operations' ? 'right':''">

          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-space :size="5">
              <el-button v-if="scope.row.status !== 2" class="correct-button" size="small"
                         @click="correctTask(scope.row.id)">批改
              </el-button>
              <el-dropdown>
                  <span>
                    <el-icon class="el-icon--right">
                      <more/>
                    </el-icon>
                  </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="scope.row.status === 3" :loading="resultSaving[scope.$index]"
                                      @click="saveResult(scope.row.id, scope.$index)">保存结果
                    </el-dropdown-item>
                    <el-dropdown-item v-if="scope.row.status !== 2" @click="correctName(scope.row.id)">姓名矫正
                    </el-dropdown-item>
                    <el-dropdown-item @click="onEditTask(scope.row)" divided>编辑</el-dropdown-item>
                    <el-dropdown-item @click="onDeleteTask(scope.row.id)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-space>
          </template>
          <template v-else-if="column.prop === 'type'" v-slot="scope">
            <el-icon>
              <Tickets/>
            </el-icon>
          </template>
          <template v-else-if="column.prop === 'record'" v-slot="scope">
            <el-link type="primary" @click="toRecord(scope.row.fileId)">查看</el-link>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag :type="statusOptions[scope.row.status].type">{{
                statusOptions[scope.row.status].label
              }}
            </el-tag>
          </template>
          <template v-else-if="column.prop === 'configName'" v-slot="scope">
            <el-link v-if="scope.row.configId" type="primary" @click="toConfig(scope.row.configId)">
              {{ scope.row.configName }}
            </el-link>
          </template>
          <template v-else-if="column.prop === 'progress'" v-slot="scope">
            <el-text v-if="scope.row.status === 2">{{
                `${scope.row.finishedCount ||
                0}/${scope.row.recordCount || 0}`
              }}
            </el-text>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer-bar">
      <el-pagination background layout="prev, pager, next" v-model:page-size="essayPapersIndexPageData.pageSize"
                     v-model:current-page="essayPapersIndexPageData.pageNumber" :total="total"
                     @current-change="loadData"/>
    </div>
    <task-form-dialog ref="taskForm" @onClose="loadData"/>
    <stats-download-form ref="statsDownloadForm" @submit="onStatsDownloadSubmit" :submiting="statsDownloading"/>
    <pdf-parse-form ref="pdfParseForm" @submit="loadData"/>
    <task-selector ref="taskSelector" @submit="doSyncDocName"/>
    <zip-file-form ref="zipFileForm" @submit="onZipSubmit" :submiting="zipFileDownloading"/>
    <!-- 文件上传表单 -->
    <right-drawer ref="fileForm" @onClose="loadData"/>
  </div>
</template>

<script>
import PdfParseForm from "../doccorrect/components/pdfparseform.vue";
import TaskFormDialog from "./components/taskFormDialog.vue";
import {useUserStore} from "@/store/index";
import {mapState} from "pinia";
import DownloadForm from "../doccorrect/components/download-form.vue";
import StatsDownloadForm from "../doccorrect/components/statsdownload-form.vue"
import TaskSelector from "../doccorrect/components/taskSelector.vue";
import ZipFileForm from "../doccorrect/components/ZipFileForm.vue";
import RightDrawer from "./components/rightDrawer.vue";
import {RefreshRight} from "@element-plus/icons-vue";
import {computed, ref} from "vue";
import {useTransition} from "@vueuse/core";

const store = useUserStore();
export default {
  components: {
    RightDrawer,
    RefreshRight,
    PdfParseForm,
    TaskFormDialog,
    DownloadForm,
    StatsDownloadForm,
    TaskSelector,
    ZipFileForm
  },
  data() {
    return {
      essayPapersIndexPageData: {
        filter: {
          name: "",
          status: "",
        },
        pageNumber: 1,
        pageSize: 10,
      },
      tasks: [],
      columns: [
        {
          prop: "type",
          label: "类型",
          width: 100
        },
        {label: "名称", prop: "name", align: 'left'},
        {label: "试卷配置", prop: "configName"},
        {label: "试卷", prop: "record", width: 125},
        {label: "状态", prop: "status", width: 200},
        {label: "进度", prop: "progress", width: 200},
        {label: "操作", prop: "operations", width: 110},
      ],

      total: 0,
      taskLoading: false,
      statusOptions: {
        1: {value: 1, label: "未批改"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      refresher: null,
      statusStats: {
        1: 0,
        2: 0,
        3: 0,
        total: 0,
      },
      downloading: false,
      statsDownloading: false,
      resultSaving: {},
      zipFileDownloading: false
    }
  },
  created() {
    const essayPapersIndexPageData = store.getEssayPapersIndexPageData;
    if (essayPapersIndexPageData) {
      this.essayPapersIndexPageData = JSON.parse(JSON.stringify(essayPapersIndexPageData));
    }

    this.loadData();
    this.loadStatusStats();
  },
  computed: {
    ...mapState(useUserStore, ["getUser"]),
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.loadData()
      vm.loadStatusStats()
    })
  },
  watch: {
    essayPapersIndexPageData: {
      handler(newVal) {
        store.setEssayPapersIndexPageData(newVal);
      },
      deep: true
    }
  },
  methods: {
    reset() {
      this.essayPapersIndexPageData = {
        filter: {
          name: "",
          status: "",
        },
        pageNumber: 1,
        pageSize: 10,
      };
      this.loadData();
    },
    goBack() {
      this.$router.back();
    },
    toConfig(configId) {
      let query = {}
      if (configId) {
        query.id = configId
      }
      this.$router.push({
        name: "docconfig",
        query,
      });
    },
    onAddTask() {
      this.$refs.taskForm.show({title: "新增作文任务"});
    },
    onEditTask(data) {
      this.$refs.taskForm.show({title: "编辑作文任务", data});
    },
    onDeleteTask(id) {
      this.$confirm("确认删除该任务吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$axios.post(`/api/docCorrectTask/delete?id=${id}`).then(res => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    loadData() {
      this.taskLoading = true
      let form = {
        page: {
          pageNumber: this.essayPapersIndexPageData.pageNumber,
          pageSize: this.essayPapersIndexPageData.pageSize,
        },
        countRecord: true,
        isEssay: 1,
      }
      form = Object.assign(form, this.essayPapersIndexPageData.filter);
      this.$axios.post("/api/docCorrectTask/page", form).then(res => {
        this.tasks = res.data.records;
        this.total = res.data.total;
      }).finally(() => {
        this.taskLoading = false;
      })
    },
    toRecord(id) {
      this.$router.push(`/essayPapers/stepEssayPapers/${id}`);
    },
    uploadDoc(response, file, fileList) {
      console.log(response)
      let files = response.data
      if (!files || files.length === 0) {
        this.$message.error("文件上传失败!");
        return;
      }
      let filename = file.name.substring(0, file.name.lastIndexOf("."));
      let form = {
        filename,
        docList: files
      };
      this.$axios.post("/api/docCorrectTask/add/file", form).then(res => {
        this.$message.success("文件上传成功");
        this.loadData();
      });
    },
    refreshTasks() {
      let isRefresh = this.statusStats[2] > 0
      if (isRefresh && !this.refresher) {
        this.refresher = setInterval(() => {
          this.loadData()
          this.loadStatusStats()
        }, 10 * 1000);
      }
      if (!isRefresh && this.refresher) {
        clearInterval(this.refresher)
        this.refresher = null
      }
    },
    downloadFile(url, name) {
      // 使用fetch获取文件内容
      return fetch(this.$fileserver.fileurl(url))
          .then(response => response.blob())
          .then(blob => {
            // 如果需要下载，可以使用前面提到的下载代码
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = URL.createObjectURL(blob);
            a.download = name;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(a.href);
          })
          .catch(error => {
            console.error('发生错误:', error);
          });
    },
    loadStatusStats() {
      this.$axios.get("/api/docCorrectTask/status/stats").then(res => {
        let rawStats = {
          1: 0,
          2: 0,
          3: 0,
          total: 0,
          rpm: res.data.rpm
        };
        res.data.stats.forEach(item => {
          rawStats[item.status] = item.count;
          if (item.status !== 1) {
            rawStats.total += item.count;
          }
        });

        const animatedStats = {};
        Object.keys(rawStats).forEach(key => {
          const source = ref(0);
          const transitionValue = useTransition(source, {
            duration: 1500,
          });
          source.value = rawStats[key];
          animatedStats[key] = computed(() => Math.round(transitionValue.value));
        });

        this.statusStats = animatedStats;
        this.refreshTasks()
      })
    },
    correctTask(taskId) {
      const store = useUserStore();
      let form = {
        id: taskId,
        aimodel: store.getDefaultCorrectModal,
        ocrType: '2',
        responseFormat: true
      }
      this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
        this.$message.success("提交成功")
        this.loadData()
        this.loadStatusStats()
      })
    },
    onStatsDownloadSubmit(formData) {
      let form = {
        taskIds: formData.tasks.map(task => task.id),
        fontSize: formData.fontSize,
        segedScore: formData.segedScore,
      }
      let loadingMessage = this.$message({
        message: "正在生成统计结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/download/stats", form).then(res => {
        return this.downloadFile(res.data.fileUrl, `${formData.tasks[0].name} 统计结果.pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
    correctName(id) {
      this.$axios.post("/api/docCorrectTask/correctName", {
        id,
      }).then(res => {
        this.$message.success("校正中，请稍后查看...")
      })
    },
    doCorrectName(command, id) {
      if (command === 'sync') {
        // 同步另一张试卷的姓名
        this.$refs.taskSelector.show({
          title: "同步试卷姓名", data: {
            id,
          }
        })
      }
    },
    doSyncDocName(formData) {
      this.$message.success("同步中，请稍后查看...")
      this.$axios.post("/api/docCorrectTask/syncDocName", formData).then(() => {
        this.$message.success("同步成功")
      })
    },
    saveResult(taskId, rowIdx) {
      this.resultSaving[rowIdx] = true
      this.$axios.post(`/api/docCorrectResult/saveTask?taskId=${taskId}`).then(res => {
        this.$message.success("保存成功")
      }).finally(() => {
        this.resultSaving[rowIdx] = false
      })
    },
    onZipSubmit(formData) {
      const tasks = formData.tasks
      if (!tasks && tasks.length === 0) {
        this.$message.error("请至少选择一份试卷")
        return
      }
      this.zipFileDownloading = true
      const name = tasks[0].name
      let loadingMessage = this.$message({
        message: "正在打包下载批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/file/zip", {
        ...formData,
        taskIds: formData.tasks.map(t => t.id),
        name,
      }).then(res => {
        this.downloadFile(res.data.fileUrl, `${name} 批改结果.zip`).then(() => {
          this.$message.success("批改结果生成成功!")
        })
      }).finally(() => {
        this.zipFileDownloading = false
        loadingMessage.close()
      })
    },
    manualRefresh() {
      this.loadData();
      this.$message({
        message: "加载成功",
        type: "success",
        duration: 1500,
      });
    }
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-page-header__header) {
  width: 100% !important;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    width: 100%;
    justify-content: space-between;

    .left-icon {
      width: 28.28px;
      height: 22.89px;
      transform: scaleX(-1);
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-right: 19px;
    }

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  .start-config {
    height: 135px;
    width: 100%;
    border-top: 2px solid #f5f5f5;
    border-bottom: 2px solid #f5f5f5;
    padding: 16px 0;
    display: flex;
    align-items: center;

    .start-button {
      cursor: pointer;
      width: 253px;
      height: 103px;
      background: #EFE9F7;
      border-radius: 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 2px solid rgba(0, 0, 0, 0.2);
        background: #ddd4e8 !important; /* 批改作文 - 紫色加深 */

        .text {
          font-weight: 700;
        }

        .icon {
          transform: scale(1.1);
        }
      }

      .icon {
        width: 55.89px;
        height: 57px;
        transition: transform 0.3s ease;
      }

      .text {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        margin-top: 4.5px;
        transition: font-weight 0.3s ease;
      }
    }
  }

  .main-content {
    margin-top: 10px;
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  // 批改按钮自定义样式
  :deep(.correct-button) {
    background-color: #1677FF !important;
    color: #FFF !important;
    border: none !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;

    &:hover {
      background-color: #4a90ff !important;
      color: #FFF !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3) !important;
    }

    &:focus {
      background-color: #4a90ff !important;
      color: #FFF !important;
    }

    &:active {
      background-color: #1677FF !important;
      color: #FFF !important;
    }
  }

  // 搜索按钮自定义样式
  :deep(.search-button) {
    background-color: #1677FF !important;
    color: #FFF !important;
    border: none !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;

    &:hover {
      background-color: #4a90ff !important;
      color: #FFF !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3) !important;
    }

    &:focus {
      background-color: #4a90ff !important;
      color: #FFF !important;
    }

    &:active {
      background-color: #1677FF !important;
      color: #FFF !important;
    }
  }
}
</style>