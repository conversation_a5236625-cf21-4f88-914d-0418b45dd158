<!-- QuestionTypeModelSelector.vue -->
<template>
  <div>
    <!-- 外层触发按钮：作为独立组件使用时可见，作为内嵌时可通过 hide-trigger 隐藏 -->
    <el-button
        v-if="!hideTrigger"
        type="primary"
        size="small"
        @click="openDialog()"
        :disabled="loading"
    >
      按题型选择模型
    </el-button>

    <el-dialog
        v-model="visible"
        title="按题型选择模型"
        width="1000px"
        :close-on-click-modal="false"
        class="question-type-model-dialog"
    >
      <el-alert
          v-if="configPackageId"
          class="tip"
          type="info"
          :closable="false"
          show-icon
      >
        <template #title>
          检测到标准卷（ID: {{ configPackageId }}）
          <el-button type="primary" link @click="prefillFromConfig" :loading="prefilling">
            填入该标准卷的模型设置？
          </el-button>
        </template>
      </el-alert>

      <el-table :data="rows" border size="small" v-loading="loading">
        <el-table-column label="题目名称" prop="label" min-width="240" />
        <el-table-column label="选择模型" min-width="640">
          <template #default="{ row }">
            <el-select
                v-model="row.selectedId"
                clearable
                filterable
                remote
                :remote-method="q => remoteSearchModels(q)"
                :loading="modelsLoading"
                placeholder="搜索并选择模型（留空=默认）"
                style="width: 100%"
            >
              <el-option
                  v-for="opt in modelOptions"
                  :key="opt.id"
                  :label="renderModelLabel(opt)"
                  :value="opt.id"
              >
                <div class="opt-line">
                  <span class="opt-label">{{ opt.name }}</span>
                  <span class="opt-sub">{{ opt.modelValue }}</span>
                </div>
              </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-space>
          <el-button @click="visible = false">关闭</el-button>
          <el-button type="warning" text @click="clearAll">清空本次选择</el-button>
          <el-button type="primary" @click="confirmAndClose">确认</el-button>
        </el-space>
      </template>
    </el-dialog>
  </div>
</template>

<script>
/**
 * QuestionTypeModelSelector.vue
 * - openDialog(initialMap?)：支持父组件传入现有选择，二次打开回填
 * - emits: confirmed → { map, resolved }
 *    - map: { [questionType]: modelSettingId }
 *    - resolved: [{questionType, modelSettingId, name, modelValue}]
 */
export default {
  name: "QuestionTypeModelSelector",
  emits: ["confirmed"],
  props: {
    configPackageId: { type: [String, Number], default: null },
    hideTrigger: { type: Boolean, default: false },
  },
  data() {
    return {
      visible: false,
      loading: false,
      prefilling: false,
      modelsLoading: false,
      bootstrapped: false,
      rows: [],         // [{ value, label, selectedId }]
      modelOptions: [], // [{ id, name, modelValue }]
    };
  },
  methods: {
    async openDialog(initialMap) {
      this.visible = true;
      if (!this.bootstrapped) {
        await this.bootstrap();
        this.bootstrapped = true;
      }
      // 回填上次选择
      if (initialMap && typeof initialMap === 'object') {
        this.applySelectionMap(initialMap);
      }
    },

    getSelectionMap() {
      const map = {};
      this.rows.forEach((r) => {
        if (r.selectedId) map[r.value] = r.selectedId;
      });
      return map;
    },

    applySelectionMap(map) {
      const next = {};
      Object.entries(map || {}).forEach(([q, id]) => {
        if (q && id) next[q] = id;
      });
      this.rows = this.rows.map((r) => ({
        ...r,
        selectedId: next[r.value] ?? r.selectedId ?? null,
      }));
    },

    async bootstrap() {
      try {
        this.loading = true;
        const qts = await this.fetchQuestionTypes();
        this.rows = qts.map((q, idx) => ({
          value: q.value,
          label: q.label,
          selectedId: null,
        }));
        await this.remoteSearchModels("");
      } catch (e) {
        console.error(e);
        this.$message.error("加载题型或模型失败");
      } finally {
        this.loading = false;
      }
    },

    async fetchQuestionTypes() {
      const res = await this.$axios.get("/api/prompts/questionTypes");
      const arr = Array.isArray(res.data) ? res.data : res.data?.records || [];
      const list = (arr || []).map((x, idx) => {
        if (typeof x === "string") return { label: x, value: x };
        return {
          label: x.label ?? x.name ?? x.value ?? `题型${idx + 1}`,
          value: x.value ?? x.name ?? x.label,
        };
      });
      const force = "分数识别";
      if (!list.some((i) => i.label === force || i.value === force)) {
        list.push({ label: force, value: force });
      }
      return list;
    },

    renderModelLabel(opt) {
      if (!opt) return "";
      return `${opt.name}${opt.modelValue ? "（" + opt.modelValue + "）" : ""}`;
    },

    async remoteSearchModels(query) {
      try {
        this.modelsLoading = true;
        const body = {
          name: query || "",
          page: { pageSize: 20, pageNumber: 1, searchCount: false },
        };
        const res = await this.$axios.post("/api/model-request/selectPage", body);
        const records = res?.data?.records || res?.data || [];
        this.modelOptions = records.map((r) => ({
          id: r.id,
          name: r.name,
          modelValue: r.modelValue,
        }));
      } catch (e) {
        console.error(e);
      } finally {
        this.modelsLoading = false;
      }
    },

    async prefillFromConfig() {
      if (!this.configPackageId) return;
      try {
        this.prefilling = true;
        const params = new URLSearchParams({
          pageNumber: "1",
          pageSize: "500",
          type: "config",
          configPackageId: String(this.configPackageId),
        });
        const res = await this.$axios.get(
            "/api/recordModelSetting/page?" + params.toString()
        );
        const list = res?.data?.records || res?.data || [];
        const map = {};
        list.forEach((it) => {
          map[it.questionType] = it.modelSettingId;
        });
        this.applySelectionMap(map);
        this.$message.success("已回填标准卷的模型设置");
      } catch (e) {
        console.error(e);
        this.$message.error("回填失败");
      } finally {
        this.prefilling = false;
      }
    },

    clearAll() {
      this.rows.forEach((r) => {
        r.selectedId = null;
      });
    },

    confirmAndClose() {
      // 组装更友好的展示数据，方便父组件渲染 tag
      const map = this.getSelectionMap();
      const id2opt = this.modelOptions.reduce((acc, o) => {
        acc[o.id] = o;
        return acc;
      }, {});
      const resolved = Object.entries(map).map(([questionType, modelSettingId]) => {
        const opt = id2opt[modelSettingId] || {};
        return {
          questionType,
          modelSettingId,
          name: opt.name,
          modelValue: opt.modelValue,
        };
      });

      this.$emit("confirmed", { map, resolved });
      this.visible = false;
      this.$message.success("已更新本次选择");
    },
  },
};
</script>

<style scoped>
.tip {
  margin-bottom: 10px;
}
.opt-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.opt-label {
  font-weight: 600;
}
.opt-sub {
  opacity: 0.6;
  font-size: 12px;
}

/* 弹窗内的按钮样式（与父页面一致的主题） */
.question-type-model-dialog :deep(.el-button--primary) {
  background-color: #1677ff !important;
  border-color: #1677ff !important;
  color: #fff !important;
  transition: all 0.3s ease;
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
}
.question-type-model-dialog :deep(.el-button--primary:hover) {
  background-color: #4a90ff !important;
  border-color: #4a90ff !important;
  color: #fff !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4);
}
.question-type-model-dialog :deep(.el-button--primary:active) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15);
}
</style>
