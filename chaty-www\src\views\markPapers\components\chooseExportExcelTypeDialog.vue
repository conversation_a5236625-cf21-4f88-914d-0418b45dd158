<template>
  <el-dialog v-model="isShow" :title="title" width="1200" :before-close="onClose">
    <el-form :model="formData" label-position="top" ref="exportForm">
      <el-form-item label="请选择导出表格样式" prop="exportStyle">
        <el-radio-group v-model="formData.exportStyle" class="radio-group">
          <el-radio :label="'style1'" class="export-style">
                <span class="download-link">
                  <a href="https://pic2.ziyuan.wang/user/tanfuhua/2025/03/详细版表格_1af5baa042469.png" download>
                    样式一：详细版表格
                  </a>
                </span>
          </el-radio>
          <el-radio :label="'style2'" style="margin-left: 100px" class="export-style">
                <span class="download-link">
                  <a href="https://pic2.ziyuan.wang/user/tanfuhua/2025/03/简化版表格_761530daf2073.png" download>
                    样式二：简化版表格（不包含每一小题的数据）
                  </a>
                </span>
          </el-radio>
<!--          <el-radio :label="'style3'" style="margin-left: 100px" class="export-style">-->
<!--                <span class="download-link">-->
<!--                  <a href="https://pic2.ziyuan.wang/user/tanfuhua/2025/03/111_3c192b044531b.jpg" download>-->
<!--                    样式三：多班级表格-->
<!--                  </a>-->
<!--                </span>-->
<!--          </el-radio>-->
<!--          <el-radio :label="'style4'" style="margin-left: 50px" class="export-style">-->
<!--                <span class="download-link">-->
<!--                  <a href="https://pic2.ziyuan.wang/user/tanfuhua/2025/04/11_3ea1f1ee11d1b.png" download>-->
<!--                    样式四：带后X名同学平均分的模板-->
<!--                  </a>-->
<!--                </span>-->
<!--          </el-radio>-->
          <el-radio :label="'style5'" style="margin-left: 150px" class="export-style">
                <span class="download-link">
                  <a href="https://pic2.ziyuan.wang/user/tanfuhua/2025/04/11_3ea1f1ee11d1b.png" download>
                    样式三+样式四+样式五+样式六：多班级表格-带后X名同学平均分的模板
                  </a>
                </span>
          </el-radio>
        </el-radio-group>
        <div style="display: flex;">
          <div class="option-image">
            <el-image src="https://pic2.ziyuan.wang/user/tanfuhua/2025/03/详细版表格_1af5baa042469.png" alt="Style 1"/>
          </div>
          <div class="option-image" style="margin-left: 50px">
            <el-image src="https://pic2.ziyuan.wang/user/tanfuhua/2025/03/简化版表格_761530daf2073.png" alt="Style 2"/>
          </div>
<!--          <div class="option-image" style="margin-left: 50px">-->
<!--            <el-image src="https://pic2.ziyuan.wang/user/tanfuhua/2025/03/111_3c192b044531b.jpg" alt="Style 3"/>-->
<!--          </div>-->
          <div class="option-image" style="margin-left: 50px">
            <el-image src="https://pic2.ziyuan.wang/user/tanfuhua/2025/04/11_3ea1f1ee11d1b.png" alt="Style 4"/>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        class="cancel-button"
        @click="onClose"
        style="background-color: #FFF !important; color: #606266 !important; border: 1px solid #DCDFE6 !important; transition: all 0.3s ease !important;"
        @mouseenter="handleCancelMouseEnter"
        @mouseleave="handleCancelMouseLeave">
        取消
      </el-button>
      <el-button
        class="confirm-button"
        @click="onSubmit"
        :loading="submitting"
        style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
        @mouseenter="handleConfirmMouseEnter"
        @mouseleave="handleConfirmMouseLeave">
        确认
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "ExportStyleDialog",
  props: {
    submitting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      title: "选择导出表格样式",
      isShow: false,
      formData: {
        exportStyle: "style2"
      }
    }
  },
  methods: {
    show() {
      this.isShow = true
    },
    onClose() {
      if (this.$refs.exportForm) {
        this.$refs.exportForm.resetFields()
      }
      this.isShow = false
    },
    onSubmit() {
      this.$emit("submit", this.formData.exportStyle)
      this.onClose()
    },
    handleConfirmMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleConfirmMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleCancelMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#F5F7FA';
      button.style.borderColor = '#C0C4CC';
      button.style.color = '#606266';
    },
    handleCancelMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#FFF';
      button.style.borderColor = '#DCDFE6';
      button.style.color = '#606266';
    }
  }
}
</script>

<style scoped>
/* 设置单选组为两列 */
.radio-group {
  display: flex;
}

.export-style {
  width: 200px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-header {
  width: 100%;
  text-align: center;
  margin-bottom: 10px;
}

.download-link a {
  color: #409EFF;
  text-decoration: none;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.download-link a:hover {
  text-decoration: underline;
}

.option-image img {
  width: 200px;
}
</style>
