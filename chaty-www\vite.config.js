import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import svgLoader from 'vite-svg-loader'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    svgLoader(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0',
    port: 20002,
    proxy: {
      '/api': 'http://127.0.0.1:20001',
      '/static': 'http://127.0.0.1:20001',
      // '/api': 'https://www.saomiaoshijuan.com',
      // '/static': 'https://www.saomiaoshijuan.com',
    }
  },
})
