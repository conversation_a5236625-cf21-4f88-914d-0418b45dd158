<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-image src="/icon/16.png" class="left-icon"></el-image>
          <el-text class="title">试卷列表</el-text>
          <el-input class="filter-item" v-model="markPaperIndexPageData.filter.name" placeholder="请输入名称"/>
          <el-select class="filter-item" v-model="markPaperIndexPageData.filter.status" placeholder="请选择状态"
                     clearable>
            <el-option v-for="item in Object.values(statusOptions)" :key="item.value" :label="item.label"
                       :value="item.value"/>
          </el-select>
          <el-button type="primary" @click="loadData">搜索</el-button>
          <el-button linked @click="reset" style="margin-left: 10px">重置</el-button>
          <el-button linked @click="manualRefresh" style="margin-left: 10px; display: flex; align-items: center;">
            <el-icon style="margin-right: 2px;">
              <RefreshRight/>
            </el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <template #extra>
        <div style="display: flex;align-items: center">
          <el-statistic class="header-action header-stats" title="批改速度" :value="statusStats.rpm"/>
          <el-statistic class="header-action header-stats" title="试卷数量" value-style="color: #E6A23C;"
                        :value="statusStats.total"/>
          <el-statistic class="header-action header-stats" title="批改中" value-style="color: #67C23A;"
                        :value="statusStats[2]"/>
          <el-statistic class="header-action header-stats" title="已批改" value-style="color: #E74C3C;"
                        :value="statusStats[3]"/>
        </div>

      </template>
    </el-page-header>


    <div class="start-config">
      <div class="start-button" type="primary" icon="EditPen" @click="onEdit">
        <el-image src="/icon/paperStart.svg" class="icon"></el-image>
        <el-text class="text">开始批改</el-text>
      </div>
    </div>

    <div class="status-list">
      <el-button class="item" :class="{'active': markPaperIndexPageData.filter.status === null}"
                 @click="changeStatusActive(null)">
        <el-text class="text">全部</el-text>
      </el-button>
      <el-button class="item" :class="{'active': markPaperIndexPageData.filter.status === 1}"
                 @click="changeStatusActive(1)">
        <el-text class="text">待批改</el-text>
      </el-button>
      <el-button class="item" :class="{'active': markPaperIndexPageData.filter.status === 2}"
                 @click="changeStatusActive(2)">
        <el-text class="text">批改中</el-text>
      </el-button>
      <el-button class="item" :class="{'active': markPaperIndexPageData.filter.status === 3}"
                 @click="changeStatusActive(3)">
        <el-text class="text">批改完成</el-text>
      </el-button>
    </div>
    <div class="main-content">
      <el-table v-loading="loading" :data="data" :row-style="getRowStyle" style="height: 100%" empty-text="无数据"
                :border="false">
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column"
                         :align="column?.align ?? 'center'"
                         :fixed="column.prop === 'operations' ? 'right':''">
          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-space :size="5" style="display: flex;flex-wrap: wrap;row-gap: 10px;justify-content: center">
              <el-button type="primary" text size="small" @click="onLook(scope.row.id)">查看</el-button>
              <el-button type="primary" text size="small" @click="editRemark(scope.row.id, scope.row.remark)">备注
              </el-button>
              <el-dropdown>
                    <span>
                      <el-icon class="el-icon--right">
                        <more/>
                      </el-icon>
                    </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="onEditName(scope.row.id, scope.row.name)">编辑名称</el-dropdown-item>
                    <el-dropdown-item @click="copy2test(scope.row.id)" divided>复制试卷到测试账户</el-dropdown-item>
                    <el-dropdown-item @click="oneClickRetest(scope.row)">一键重测</el-dropdown-item>
                    <el-dropdown-item @click="onDelete(scope.row.id)" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-space>

          </template>
          <template v-else-if="column.prop === 'type'" v-slot="scope">
            <el-icon>
              <DocumentAdd/>
            </el-icon>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag :type="statusOptions[scope.row.status]?.type">
              {{ statusOptions[scope.row.status]?.label }}
            </el-tag>
          </template>
          <template v-else-if="column.prop === 'isCorrectFinish'" v-slot="scope">
            <el-tag v-if="scope.row.status !== 2" :type="isCorrectFinishOptions[scope.row.isCorrectFinish]?.type">
              {{ isCorrectFinishOptions[scope.row.isCorrectFinish]?.label }}
            </el-tag>
            <el-text v-else>已批改{{ getMinutesAgo(scope.row?.lastCorrectTime) }}</el-text>
          </template>
          <template v-else-if="column.prop === 'remark'" v-slot="scope">
            {{ scope.row?.remark ?? '-' }}
          </template>
          <template v-else-if="column.prop === 'name'" v-slot="scope">
            <el-link type="primary" :underline="false" @click="onLook(scope.row.id)">{{ scope.row.name }}</el-link>
          </template>
          <template v-else-if="column.prop === 'recordSize'" v-slot="scope">
            {{ scope.row?.recordSize ? (scope.row?.recordSize + '份') : '-' }}
          </template>
          <template v-else-if="column.prop === 'createTime'" v-slot="scope">
            {{
              $getFeiShuTimeFormat(scope.row?.createTime)
            }}
          </template>
          <template v-else-if="column.prop === 'modelRequestId'" v-slot="scope">
            <el-link
                type="primary"
                underline
                style="cursor: pointer;"
                @click="showModelDetail(scope.row.id)"
            >
              {{ getModelName(scope.row.modelRequestId) }}
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer-bar">
      <el-pagination background layout="prev, pager, next" v-model:page-size="markPaperIndexPageData.pageSize"
                     v-model:current-page="markPaperIndexPageData.pageNumber" :total="total"
                     @current-change="loadData"/>
    </div>

    <!-- 文件上传表单 -->
    <right-drawer ref="fileForm" @onClose="loadData"/>

    <model-paper-dialog ref="modelPaperDialog" @close="loadData"></model-paper-dialog>

    <el-dialog
        title="查看 JSON 数据"
        v-model="jsonDialogVisible"
        width="800px"
    >
      <json-viewer :value="currentJson" expand-depth="2"/>
      <template #footer>
        <el-button @click="jsonDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <recordModelSettingDialog ref="recordModelSettingDialog"></recordModelSettingDialog>
  </div>
</template>
<script>
import RightDrawer from './components/rightDrawer.vue'
import {computed, ref} from "vue";
import {useTransition} from '@vueuse/core'
import {More, RefreshRight} from "@element-plus/icons-vue";
import {useUserStore} from "@/store";
import ModelPaperDialog from '@/views/ModelRequest/ModelPaperDialog.vue'
import recordModelSettingDialog from "@/views/ModelRequest/recordModelSettingDialog.vue";
const store = useUserStore();
export default {
  components: {
    More,
    RefreshRight,
    RightDrawer,
    ModelPaperDialog,
    recordModelSettingDialog
  },
  data() {
    return {
      statusStats: {
        1: 0,
        2: 0,
        3: 0,
        total: 0,
      },
      markPaperIndexPageData: {
        filter: {
          name: "",
          status: null,
        },
        pageSize: 10,
        pageNumber: 1
      },
      loading: false,
      data: [],
      columns: [
        {
          prop: "type",
          label: "类型",
          width: 100
        },
        {
          prop: "name",
          label: "试卷名称",
          align: "left",
        },
        {
          prop: "modelRequestId",
          label: "模型名称",
        },
        {
          prop: "recordSize",
          label: "份数",
          width: 70
        },
        {
          prop: "status",
          label: "状态",
          width: 125
        },
        {
          prop: "isCorrectFinish",
          label: "进度",
          width: 125
        },
        {
          prop: "remark",
          label: "备注",
          width: 350
        },
        {
          prop: "createTime",
          label: "上传时间",
          width: 200
        },
        {
          prop: "operations",
          label: "操作",
          width: 200
        },
      ],
      total: 0,
      statusOptions: {
        1: {value: 1, label: "待批改"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      isCorrectFinishOptions: {
        0: {value: 2, label: "纠错中", type: "warning"},
        1: {value: 3, label: "已完成", type: "success"},
      },
      timer: null,
      colorPalette: [
        '#e0ece0',
        '#f1f6f0',
        '#eef5fb',
        '#ECEFF1'
      ],
      nameColorMap: {},
      colorPaletteIdx: 0,
      jsonDialogVisible: false,
      currentJson: null,
      dialogVisible: false,
      isEdit: false
    }
  },
  created() {
    const markPaperIndexPageData = store.getMarkPaperIndexPageData;
    if (markPaperIndexPageData) {
      this.markPaperIndexPageData = JSON.parse(JSON.stringify(markPaperIndexPageData));
    }
    this.loadData()
    this.loadStatusStats();
    this.timer = setInterval(() => {
      this.loadData();
      this.loadStatusStats();
    }, 10 * 1000);
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  watch: {
    markPaperIndexPageData: {
      handler(newVal) {
        store.setMarkPaperIndexPageData(newVal);
      },
      deep: true
    }
  },
  computed: {},
  methods: {
    oneClickRetest(row) {
      this.$refs.modelPaperDialog.show({
        id: row.id,
        paperName: row.name,
        modelValue: null
      })
    },
    /**
     * 计算从 lastTime 到当前的时间差（分秒形式），如 "1分30s" 或 "26s"
     * @param {string|number|Date} lastTime — 最后批改时间
     * @returns {string} 分秒格式的时间差
     */
    getMinutesAgo(lastTime) {
      if (!lastTime) return '-';
      const lastTs = new Date(lastTime).getTime();
      if (isNaN(lastTs)) return '--';

      const diffMs = Date.now() - lastTs;
      if (diffMs < 0) return '0s'; // 如果传入的时间在未来，直接返回 0s

      const totalSeconds = Math.floor(diffMs / 1000);
      if (totalSeconds < 60) {
        // 小于 1 分钟，只显示秒
        return `${totalSeconds}s`;
      }

      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      return `${minutes}分${seconds}s`;
    },
    reset() {
      this.markPaperIndexPageData = {
        filter: {
          name: "",
          status: null,
        },
        pageSize: 10,
        pageNumber: 1
      }
      this.loadData();
    },
    calculateNameColorMap(data) {
      const freq = {};
      data.forEach(row => {
        if (row.configIds) {
          freq[row.configIds] = (freq[row.configIds] || 0) + 1;
        }
      });

      const map = this.nameColorMap;
      let idx = this.colorPaletteIdx;
      const colorPalette = this.colorPalette;

      data.forEach(row => {
        const configIds = row.configIds;
        if (configIds && freq[configIds] > 1 && !map[configIds] && configIds !== '[]') {
          map[configIds] = colorPalette[idx % colorPalette.length];
          idx++;
        }
      });

      this.nameColorMap = map;
      this.colorPaletteIdx = idx;
    },
    getRowStyle({row}) {
      const bg = this.nameColorMap[row.configIds];
      return bg ? {background: bg} : {};
    },
    loadStatusStats() {
      this.$axios.get("/api/docCorrectTask/status/stats").then(res => {
        let rawStats = {
          1: 0,
          2: 0,
          3: 0,
          total: 0,
          rpm: res.data.rpm
        };
        res.data.stats.forEach(item => {
          rawStats[item.status] = item.count;
          if (item.status !== 1) {
            rawStats.total += item.count;
          }
        });

        const animatedStats = {};
        Object.keys(rawStats).forEach(key => {
          const source = ref(0);
          const transitionValue = useTransition(source, {
            duration: 1500,
          });
          source.value = rawStats[key];
          animatedStats[key] = computed(() => Math.round(transitionValue.value));
        });

        this.statusStats = animatedStats;
      })
    },
    goBack() {
      this.$router.back();
    },
    changeStatusActive(e) {
      this.markPaperIndexPageData.filter.status = e;
      this.loadData();
    },
    onDelete(id) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        this.$axios.post(`/api/docCorrectFile/delete?id=${id}`).then(res => {
          this.$message.success("删除成功")
          this.loadData()
        })
      })
    },
    copy2test(id) {
      this.$confirm("是否确认复制试卷到测试账户?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        this.$axios.get(`/api/docCorrectFile/copy2testAccount?fileId=${id}`).then(res => {
          this.$message.success("复制成功")
          this.loadData()
        })
      })
    },
    /**
     * 点击“编辑名称”按钮时调用，弹出输入框修改试卷名称，且在提交前做名称重复检查
     * @param {number} id - 当前行的试卷 ID
     * @param {string} currentName - 当前试卷的名称，用于在输入框中显示初始值
     */
    onEditName(id, currentName) {
      // 使用 Element Plus 的 $prompt 弹出对话框
      this.$prompt('请输入新的试卷名称', '编辑名称', {
        inputValue: currentName,            // 初始值为当前名称
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '新的试卷名称',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '名称不能为空';
          }
          return true;
        },
        inputErrorMessage: '名称不能为空',
      })
          .then(({value}) => {
            const newName = value.trim();
            // 在提交更新之前，先调用后端接口检查名称是否重复
            this.$axios
                .get(`/api/docCorrectFile/checkName?name=${encodeURIComponent(newName)}`)
                .then((res) => {
                  // 假设后端返回 res.data.exists 表示该名称是否已存在
                  // 如果接口返回字段不同，请根据实际情况调整下面的判断逻辑
                  if (res.data?.exists) {
                    this.$message.error('试卷名称已存在，请使用其他名称');
                    return;
                  }
                  // 名称不重复，继续调用更新接口
                  const payload = {
                    id: id,
                    name: newName,
                  };
                  this.$axios
                      .post('/api/docCorrectFile/update', payload)
                      .then(() => {
                        this.$message.success('修改成功！');
                        this.loadData();
                      })
                      .catch((err) => {
                        console.error(err);
                        this.$message.error('修改失败，请稍后重试');
                      });
                })
                .catch((err) => {
                  console.error(err);
                  this.$message.error('检查名称时发生错误，请稍后重试');
                });
          })
          .catch(() => {
            // 用户点击“取消”或关闭弹窗
            this.$message.info('已取消修改名称');
          });
    },
    loadData() {
      this.loading = true
      return this.$axios.post("/api/docCorrectFile/page", {
        page: {
          pageNumber: this.markPaperIndexPageData.pageNumber,
          pageSize: this.markPaperIndexPageData.pageSize
        },
        name: this.markPaperIndexPageData.filter.name,
        status: this.markPaperIndexPageData.filter.status,
        isEssay: 0,
        needConfigIds: true
      }).then(res => {
        this.calculateNameColorMap(res.data.records);
        this.data = res.data.records
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },
    onEdit() {
      this.$router.push({path: `/markPapers/upload`})
    },
    editRemark(id, remark) {
      this.$prompt('请输入新的备注', '修改备注', {
        inputValue: remark ?? '',
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(({value}) => {
        const param = {
          id: id,
          remark: value
        }
        this.$axios.post("/api/docCorrectFile/update", param).then(res => {
          // 更新
          this.$message.success("修改成功！")
          this.loadData();
        })
      }).catch((e) => {
        // 用户点击取消时的处理逻辑
        this.$message.info("已取消修改");
      });
    },
    onLook(id) {
      this.$router.push({path: `/markPapers/stepMarkPapers/${id}`})
    },
    toDetail(id) {
      this.$router.push({path: `/docfile/${id}`})
    },
    async manualRefresh() {
      try {
        await this.loadData();
        this.$message({
          message: "加载成功",
          type: "success",
          duration: 1500,
        });
      } catch (e) {
        this.$message.error("刷新失败");
      }
    },
    getModelName(id) {
      const opts = Object.values(store.getAimodelOptions)
      const found = opts.find(o => o.modelRequestId === id)
      return found ? found.label : '查看'
    },
    showModelDetail(id) {
      this.$refs.recordModelSettingDialog.show({fileId : id});
    }
  }
}
</script>
<style lang="scss" scoped>
.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: scroll; /* 强制显示滚动机制 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;

    .left-icon {
      width: 28.28px;
      height: 22.89px;
      transform: scaleX(-1);
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-right: 19px;
    }

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  .start-config {
    height: 135px;
    width: 100%;
    border-top: 2px solid #f5f5f5;
    border-bottom: 2px solid #f5f5f5;
    padding: 16px 0;
    display: flex;
    align-items: center;

    .start-button {
      width: 253px;
      height: 103px;
      background: #e1f2ff;
      border-radius: 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 2px solid rgba(0, 0, 0, 0.2);
        background: #c7e7ff !important; /* 开始批改 - 蓝色加深 */

        .text {
          font-weight: 700;
        }

        .icon {
          transform: scale(1.1);
        }
      }

      .icon {
        width: 55.89px;
        height: 57px;
        transition: transform 0.3s ease;
      }

      .text {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        margin-top: 4.5px;
        transition: font-weight 0.3s ease;
      }
    }
  }

  .status-list {
    margin: 15px 0;

    .item {
      height: 32px;
      background: #F5F6F7;
      border-radius: 4px;
      padding: 6px 25px;
      border: none;

      .text {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        letter-spacing: 0;
        text-align: center;
      }
    }

    .active {
      background: #3981ff24;
      border: 2px solid #3981FF !important;

      .text {
        color: #3981FF;
        font-weight: 700;
      }
    }
  }

  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  /* 搜索按钮样式 */
  :deep(.el-button--primary) {
    background-color: #1677FF !important;
    border-color: #1677FF !important;
    color: #FFF !important;
    transition: all 0.3s ease;
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);

    &:hover {
      background-color: #4a90ff !important;
      border-color: #4a90ff !important;
      color: #FFF !important;
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15);
    }
  }

  /* 查看、备注按钮样式 */
  :deep(.el-button--primary.is-text) {
    background-color: #1677FF !important;
    border-color: #1677FF !important;
    color: #FFF !important;
    transition: all 0.3s ease;
    transform: translateY(0);
    padding: 8px 15px;
    border-radius: 6px;
    position: relative;
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);

    &:hover {
      background-color: #4a90ff !important;
      border-color: #4a90ff !important;
      color: #FFF !important;
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15);
    }
  }
}
</style>