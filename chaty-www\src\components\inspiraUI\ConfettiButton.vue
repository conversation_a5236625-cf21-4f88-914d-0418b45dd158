<template>
  <div class="relative flex h-24 w-full flex-col items-center justify-center">
    <el-button
        type="primary"
        class="confetti-save-button"
        icon="Check"
        @click="handleClick"
    >
      保存
    </el-button>
  </div>
</template>

<script setup lang="ts">
import confetti from "canvas-confetti";

// Function to trigger the confetti side cannons
function handleClick() {
  const end = Date.now() + 3 * 1000; // 3 seconds
  const colors = ["#a786ff", "#fd8bbc", "#eca184", "#f8deb1"];

  // Frame function to trigger confetti cannons
  function frame() {
    if (Date.now() > end) return;

    // Left side confetti cannon
    confetti({
      particleCount: 2,
      angle: 60,
      spread: 55,
      startVelocity: 60,
      origin: { x: 0, y: 0.5 },
      colors: colors,
    });

    // Right side confetti cannon
    confetti({
      particleCount: 2,
      angle: 120,
      spread: 55,
      startVelocity: 60,
      origin: { x: 1, y: 0.5 },
      colors: colors,
    });

    requestAnimation<PERSON>rame(frame); // Keep calling the frame function
  }

  frame();
}

</script>

<style scoped>
.confetti-save-button.el-button--primary {
  background-color: #1677FF !important;
  border-color: #1677FF !important;
  color: #FFF !important;
  transition: all 0.3s ease;
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
}

.confetti-save-button.el-button--primary:hover {
  background-color: #4a90ff !important;
  border-color: #4a90ff !important;
  color: #ffffff !important; /* 确保文字保持白色 */
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4);
}

.confetti-save-button.el-button--primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15);
}
</style>