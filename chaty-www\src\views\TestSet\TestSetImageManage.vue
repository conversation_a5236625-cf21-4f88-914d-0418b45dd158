<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchFormRef" :model="searchForm" class="header-form">
          <el-form-item label="测试集ID">
            <el-input
                v-model="searchForm.testSetId"
                placeholder="请输入测试集ID"
                clearable
                style="width: 220px;"
            />
          </el-form-item>

          <el-form-item label="题型">
            <el-select
                v-model="searchForm.questionType"
                placeholder="请选择题型"
                clearable
                filterable
                :loading="qtypeLoading"
                style="width: 220px;"
            >
              <el-option v-for="opt in questionTypeOptions" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>

          <el-form-item label="来源试卷ID">
            <el-input
                v-model="searchForm.fromRecordId"
                placeholder="请输入来源试卷ID"
                clearable
                style="width: 220px;"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" :loading="loading" @click="loadData">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="success" @click="openDialog()">新增</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <!-- 表格 -->
    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          empty-text="无数据"
          row-key="id"
          border
      >
        <el-table-column type="index" label="序号" width="80" />

        <el-table-column label="图片" width="120">
          <template #default="{ row }">
            <el-image
                v-if="row.imgUrl"
                :src="row.imgUrl"
                :preview-src-list="[row.imgUrl]"
                fit="cover"
                style="width: 80px; height: 80px; border-radius: 6px;"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="imgUrl" label="图片地址" min-width="240">
          <template #default="{ row }">
            <a v-if="row.imgUrl" :href="row.imgUrl" target="_blank" class="simple-link">{{ row.imgUrl }}</a>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="题型" width="140">
          <template #default="{ row }">
            <el-tag size="small" v-if="row.questionType">{{ row.questionType }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="是否使用题目信息" width="160">
          <template #default="{ row }">
            <el-tag :type="row.useQuestionDetail ? 'success' : 'info'" size="small">
              {{ row.useQuestionDetail ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="来源" min-width="220">
          <template #default="{ row }">
            <div>
              <span class="muted">试卷ID：</span>{{ row.fromRecordId || '-' }}
            </div>
            <div>
              <span class="muted">区域号：</span>{{ row.fromRecordAreaIdx ?? '-' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="JSON" width="160">
          <template #default="{ row }">
            <el-tag size="small" :type="row.rightAnswer ? 'primary' : 'info'">答案{{ row.rightAnswer ? '有' : '无' }}</el-tag>
            <el-tag size="small" :type="row.questionDetail ? 'primary' : 'info'" style="margin-left: 6px;">题面{{ row.questionDetail ? '有' : '无' }}</el-tag>
            <el-button type="primary" link style="margin-left: 8px;" @click="openJsonViewer(row)">查看</el-button>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />

        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="openDialog(row)">编辑</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="isEdit ? '编辑图片信息' : '新增图片信息'" v-model="dialogVisible" width="820px">
      <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogRules" label-width="140px">
        <el-form-item label="测试集ID" prop="testSetId">
          <el-input v-model="dialogForm.testSetId" placeholder="请输入测试集ID" />
        </el-form-item>

        <el-form-item label="图片地址" prop="imgUrl">
          <el-input v-model="dialogForm.imgUrl" placeholder="请输入图片URL" />
        </el-form-item>

        <el-form-item label="题型" prop="questionType">
          <el-select
              v-model="dialogForm.questionType"
              placeholder="请选择题型"
              filterable
              clearable
              :loading="qtypeLoading"
              style="width: 100%;"
          >
            <el-option v-for="opt in questionTypeOptions" :key="opt" :label="opt" :value="opt" />
          </el-select>
        </el-form-item>

        <el-form-item label="是否使用题面">
          <el-switch v-model="dialogForm.useQuestionDetail" />
        </el-form-item>

        <el-form-item label="来源试卷ID">
          <el-input v-model="dialogForm.fromRecordId" placeholder="可选" />
        </el-form-item>

        <el-form-item label="来源区域号">
          <el-input v-model.number="dialogForm.fromRecordAreaIdx" placeholder="数字，可选" />
        </el-form-item>

        <el-divider content-position="left">JSON 字段</el-divider>

        <el-form-item label="正确答案 JSON" prop="rightAnswerStr">
          <el-input
              v-model="dialogForm.rightAnswerStr"
              type="textarea"
              :rows="6"
              placeholder='如：{"label":"A","score":5}'
          />
          <el-button link type="primary" @click="formatJson('rightAnswerStr')" style="margin-left:8px;">美化</el-button>
          <el-button link @click="clearJson('rightAnswerStr')">清空</el-button>
        </el-form-item>

        <el-form-item label="题目信息 JSON" prop="questionDetailStr">
          <el-input
              v-model="dialogForm.questionDetailStr"
              type="textarea"
              :rows="6"
              placeholder='如：{"stem":"……","options":["A","B","C","D"]}'
          />
          <el-button link type="primary" @click="formatJson('questionDetailStr')" style="margin-left:8px;">美化</el-button>
          <el-button link @click="clearJson('questionDetailStr')">清空</el-button>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submitDialog">确定</el-button>
      </template>
    </el-dialog>

    <!-- JSON 查看弹窗 -->
    <el-dialog title="查看 JSON" v-model="jsonDialog.visible" width="740px">
      <div class="json-block">
        <div class="json-title">正确答案</div>
        <pre class="json-pre">{{ jsonDialog.rightAnswerPretty }}</pre>
      </div>
      <div class="json-block">
        <div class="json-title">题目信息</div>
        <pre class="json-pre">{{ jsonDialog.questionDetailPretty }}</pre>
      </div>
      <template #footer>
        <el-button type="primary" @click="jsonDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TestSetImageManage',
  data() {
    return {
      loading: false,
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,

      searchForm: {
        testSetId: '',
        questionType: '',
        fromRecordId: ''
      },

      dialogVisible: false,
      dialogLoading: false,
      isEdit: false,
      dialogForm: {
        id: null,
        testSetId: '',
        imgUrl: '',
        questionType: '',
        useQuestionDetail: true,
        fromRecordId: '',
        fromRecordAreaIdx: null,
        rightAnswerStr: '',
        questionDetailStr: ''
      },
      dialogRules: {
        testSetId: [{ required: true, message: '请输入测试集ID', trigger: 'blur' }],
        imgUrl: [{ required: true, message: '请输入图片地址', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择题型', trigger: 'change' }],
        rightAnswerStr: [{ validator: (r, v, cb) => this.jsonValidator(v, cb), trigger: 'blur' }],
        questionDetailStr: [{ validator: (r, v, cb) => this.jsonValidator(v, cb), trigger: 'blur' }]
      },

      questionTypeOptions: [],
      qtypeLoading: false,

      jsonDialog: {
        visible: false,
        rightAnswerPretty: '',
        questionDetailPretty: ''
      }
    };
  },
  created() {
    // 路由预置 testSetId
    const preId = this.$route?.query?.testSetId;
    if (preId) this.searchForm.testSetId = String(preId);
    this.fetchQuestionTypes();
    this.loadData();
  },
  methods: {
    goBack() {
      this.$router && this.$router.back && this.$router.back();
    },

    // 获取题型选项
    async fetchQuestionTypes() {
      this.qtypeLoading = true;
      try {
        const res = await this.$axios.get('/api/prompts/questionTypes');
        const list = res?.data?.data ?? res?.data ?? [];
        this.questionTypeOptions = Array.isArray(list) ? list : [];
      } finally {
        this.qtypeLoading = false;
      }
    },

    // 列表
    async loadData() {
      this.loading = true;
      try {
        const params = {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
          testSetId: this.searchForm.testSetId || undefined,
          questionType: this.searchForm.questionType || undefined,
          fromRecordId: this.searchForm.fromRecordId || undefined
        };
        const res = await this.$axios.get('/api/testsetImage/page', { params });
        const pageObj = res?.data?.data ?? res?.data ?? {};
        this.tableData = pageObj.records || [];
        this.total = pageObj.total || 0;
        this.pageSize = pageObj.size || this.pageSize;
        this.pageNumber = pageObj.current || this.pageNumber;
      } finally {
        this.loading = false;
      }
    },

    reset() {
      this.searchForm = { testSetId: '', questionType: '', fromRecordId: '' };
      this.pageNumber = 1;
      this.loadData();
    },

    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },

    // 打开新增/编辑
    openDialog(row = null) {
      if (row) {
        this.isEdit = true;
        this.dialogForm = {
          id: row.id,
          testSetId: row.testSetId ?? '',
          imgUrl: row.imgUrl ?? '',
          questionType: row.questionType ?? '',
          useQuestionDetail: typeof row.useQuestionDetail === 'boolean' ? row.useQuestionDetail : true,
          fromRecordId: row.fromRecordId ?? '',
          fromRecordAreaIdx: row.fromRecordAreaIdx ?? null,
          rightAnswerStr: this.pretty(row.rightAnswer) || '',
          questionDetailStr: this.pretty(row.questionDetail) || ''
        };
      } else {
        this.isEdit = false;
        // 如果查询区已有 testSetId，带入
        const defaultTs = this.searchForm.testSetId ? String(this.searchForm.testSetId) : '';
        this.dialogForm = {
          id: null,
          testSetId: defaultTs,
          imgUrl: '',
          questionType: '',
          useQuestionDetail: true,
          fromRecordId: '',
          fromRecordAreaIdx: null,
          rightAnswerStr: '',
          questionDetailStr: ''
        };
      }
      if (!this.questionTypeOptions.length) this.fetchQuestionTypes();
      this.dialogVisible = true;
    },

    // JSON 工具
    jsonValidator(v, cb) {
      if (!v || !String(v).trim()) return cb(); // 允许空
      try {
        JSON.parse(v);
        cb();
      } catch (e) {
        cb(new Error('JSON 格式不正确'));
      }
    },
    pretty(obj) {
      try {
        if (obj == null) return '';
        if (typeof obj === 'string') {
          return JSON.stringify(JSON.parse(obj), null, 2);
        }
        return JSON.stringify(obj, null, 2);
      } catch {
        return String(obj || '');
      }
    },
    formatJson(field) {
      const v = this.dialogForm[field];
      if (!v) return;
      try {
        this.dialogForm[field] = JSON.stringify(JSON.parse(v), null, 2);
      } catch {
        this.$message.error('JSON 格式不正确');
      }
    },
    clearJson(field) {
      this.dialogForm[field] = '';
    },

    // 提交
    async submitDialog() {
      this.$refs.dialogFormRef.validate(async valid => {
        if (!valid) return;
        this.dialogLoading = true;
        try {
          const payload = {
            id: this.dialogForm.id,
            testSetId: this.dialogForm.testSetId ? Number(this.dialogForm.testSetId) : null,
            imgUrl: (this.dialogForm.imgUrl || '').trim(),
            questionType: this.dialogForm.questionType || '',
            useQuestionDetail: !!this.dialogForm.useQuestionDetail,
            fromRecordId: this.dialogForm.fromRecordId || null,
            fromRecordAreaIdx:
                this.dialogForm.fromRecordAreaIdx === '' || this.dialogForm.fromRecordAreaIdx == null
                    ? null
                    : Number(this.dialogForm.fromRecordAreaIdx),
            rightAnswer: this.dialogForm.rightAnswerStr ? JSON.parse(this.dialogForm.rightAnswerStr) : null,
            questionDetail: this.dialogForm.questionDetailStr ? JSON.parse(this.dialogForm.questionDetailStr) : null
          };

          if (this.isEdit) {
            await this.$axios.post('/api/testsetImage/update', payload);
            this.$message.success('更新成功');
          } else {
            await this.$axios.post('/api/testsetImage/add', payload);
            this.$message.success('新增成功');
          }
          this.dialogVisible = false;
          this.loadData();
        } catch (e) {
          // JSON.parse 失败等
          this.$message.error(e?.message || '提交失败');
        } finally {
          this.dialogLoading = false;
        }
      });
    },

    // 删除
    async onDelete(id) {
      await this.$confirm('确认删除该记录？', '警告', { type: 'warning' });
      await this.$axios.delete(`/api/testsetImage/${id}`);
      this.$message.success('删除成功');
      this.loadData();
    },

    // JSON 查看
    openJsonViewer(row) {
      this.jsonDialog.rightAnswerPretty = this.pretty(row.rightAnswer) || '(空)';
      this.jsonDialog.questionDetailPretty = this.pretty(row.questionDetail) || '(空)';
      this.jsonDialog.visible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.simple-link {
  color: #1890ff;
  text-decoration: underline;
}
.simple-link:hover {
  color: #40a9ff;
}

.muted {
  color: var(--el-text-color-secondary);
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
    background: #fff;
    padding: 12px;
    border-radius: 8px;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.json-block { margin-bottom: 12px; }
.json-title {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-bottom: 6px;
}
.json-pre {
  background: #0f172a;
  color: #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  max-height: 360px;
  overflow: auto;
  font-size: 12px;
  line-height: 1.5;
}
</style>
