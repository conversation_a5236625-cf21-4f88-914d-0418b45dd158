<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchFormRef" :model="searchForm" class="header-form">
          <el-form-item label="名称">
            <el-input
                v-model="searchForm.name"
                placeholder="请输入名称"
                clearable
                style="width: 220px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="loadData">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="success" @click="openDialog()">新增</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <!-- 表格 -->
    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          empty-text="无数据"
      >
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="name" label="测试集名称" width="240" />

        <el-table-column prop="description" label="描述">
          <template #default="{ row }">
            <el-tooltip effect="dark" :content="row.description || ''" placement="top">
              <span>
                {{
                  (row.description || '').length > 40
                      ? (row.description || '').slice(0, 40) + '...'
                      : (row.description || '-')
                }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="包含题型" min-width="260">
          <template #default="{ row }">
            <template v-if="row.questionTypes">
              <el-tag
                  v-for="(q, idx) in splitTypes(row.questionTypes)"
                  :key="idx"
                  class="mr-6"
                  size="small"
              >
                {{ q }}
              </el-tag>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 新增列：模型名称（查看按钮） -->
        <el-table-column label="模型名称" width="160">
          <template #default="{ row }">
            <el-button type="primary" link @click="openRecordModelSetting(row.id)">查看</el-button>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="200" />
        <el-table-column prop="updateTime" label="更新时间" width="200" />

        <el-table-column label="操作" fixed="right" width="260">
          <template #default="{ row }">
            <el-link type="primary" @click="openDialog(row)">编辑</el-link>
            <el-divider direction="vertical" />
            <!-- ✅ 新增：图片管理入口 -->
            <el-link type="info" @click="goImageManage(row)">图片管理</el-link>
            <el-divider direction="vertical" />
            <el-link type="primary" @click="$refs.resultDialog.show({ testSetId: row.id })">批改结果</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="isEdit ? '编辑测试集' : '新增测试集'" v-model="dialogVisible" width="700px">
      <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogRules" label-width="140px">
        <el-form-item label="测试集名称" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入测试集名称" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
              v-model="dialogForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入描述"
              maxlength="512"
              show-word-limit
          />
        </el-form-item>

        <!-- 题型：下拉多选（来自 /api/prompts/questionTypes） -->
        <el-form-item label="包含题型">
          <el-select
              v-model="dialogForm.questionTypesArr"
              multiple
              filterable
              placeholder="请选择题型"
              style="width: 100%;"
              :loading="qtypeLoading"
          >
            <el-option
                v-for="opt in questionTypeOptions"
                :key="opt"
                :label="opt"
                :value="opt"
            />
          </el-select>
          <div class="el-form-item__extra">
            题型选项来源于系统字典（不可自定义）。
          </div>
        </el-form-item>

        <!-- 仅新增时出现：模型参数 -->
        <el-form-item v-if="!isEdit" label="模型参数">
          <QuestionTypeModelSelector ref="qsModelSelector" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submitDialog">确定</el-button>
      </template>
    </el-dialog>
    <TestSetResultDialog ref="resultDialog" />
    <!-- 模型绑定查看 Dialog -->
    <recordModelSettingDialog ref="recordModelSettingDialog"></recordModelSettingDialog>
  </div>
</template>

<script>
import recordModelSettingDialog from "@/views/ModelRequest/recordModelSettingDialog.vue";
import QuestionTypeModelSelector from '@/views/QuestionType/QuestionTypeModelSelector.vue';
import TestSetResultDialog from './TestSetResultDialog.vue';
export default {
  name: 'TestSetManage',
  components: {
    recordModelSettingDialog,
    QuestionTypeModelSelector,
    TestSetResultDialog
  },
  data() {
    return {
      loading: false,
      searchForm: { name: '' },
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,

      dialogVisible: false,
      isEdit: false,
      dialogLoading: false,
      dialogForm: {
        id: null,
        name: '',
        description: '',
        // 多选值用数组维护；提交时 join(',')
        questionTypesArr: []
      },
      dialogRules: {
        name: [{ required: true, message: '测试集名称不能为空', trigger: 'blur' }]
      },

      // 题型下拉选项
      questionTypeOptions: [],
      qtypeLoading: false
    };
  },
  created() {
    this.loadData();
    this.fetchQuestionTypes();
  },
  methods: {
    goImageManage(row) {
      // 跳转并携带 testSetId；图片管理页已支持 ?testSetId=
      this.$router.push({
        name: 'TestSetImageManage',
        query: { testSetId: row.id }
      });
    },
    goBack() {
      this.$router && this.$router.back && this.$router.back();
    },

    splitTypes(str) {
      if (!str) return [];
      return str.split(',').map(s => (s || '').trim()).filter(Boolean);
    },

    // 加载题型候选
    async fetchQuestionTypes() {
      this.qtypeLoading = true;
      try {
        const res = await this.$axios.get('/api/prompts/questionTypes');
        // 接口返回 List<String>
        const list = res?.data?.data ?? res?.data ?? [];
        this.questionTypeOptions = Array.isArray(list) ? list : [];
      } finally {
        this.qtypeLoading = false;
      }
    },

    // 列表（与后端分页结构对齐；若服务端是 POST + params，可改成 post + { params }）
    async loadData() {
      this.loading = true;
      try {
        const params = {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
          name: this.searchForm.name || undefined
        };
        const res = await this.$axios.get('/api/testset/page', { params });
        const pageObj = res?.data?.data ?? res?.data ?? {};
        this.tableData = pageObj.records || [];
        this.total = pageObj.total || 0;
        this.pageSize = pageObj.size || this.pageSize;
        this.pageNumber = pageObj.current || this.pageNumber;
      } finally {
        this.loading = false;
      }
    },

    reset() {
      this.searchForm = { name: '' };
      this.pageNumber = 1;
      this.loadData();
    },

    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },

    openDialog(row = null) {
      if (row) {
        // 编辑
        this.isEdit = true;
        this.dialogForm = {
          id: row.id,
          name: row.name || '',
          description: row.description || '',
          questionTypesArr: this.splitTypes(row.questionTypes || '')
        };
      } else {
        // 新增
        this.isEdit = false;
        this.dialogForm = {
          id: null,
          name: '',
          description: '',
          questionTypesArr: []
        };
      }
      // 确保下拉选项已加载
      if (!this.questionTypeOptions.length) {
        this.fetchQuestionTypes();
      }
      this.dialogVisible = true;
    },

    // 将 QuestionTypeModelSelector 的 Map 转成后端需要的 List<RecordModelSettingEntity>
    buildRecordModelSettingList() {
      const selMap = this.$refs.qsModelSelector?.getSelectionMap?.() || {};
      const list = [];
      // 兼容 Map/Object 两种
      const entries = selMap instanceof Map ? selMap.entries() : Object.entries(selMap);
      for (const [questionType, value] of entries) {
        if (!questionType) continue;
        // value 可能是 number（modelSettingId），或对象 { id / modelSettingId / ... }
        let modelSettingId = null;
        if (typeof value === 'number') {
          modelSettingId = value;
        } else if (value && typeof value === 'object') {
          modelSettingId = value.modelSettingId ?? value.id ?? null;
        }
        if (modelSettingId) {
          list.push({
            questionType,
            modelSettingId
            // testSetId 与 type 后端会补：testSetId=新建后的ID；type="testSet"
          });
        }
      }
      return list;
    },

    async submitDialog() {
      this.$refs.dialogFormRef.validate(async valid => {
        if (!valid) return;
        this.dialogLoading = true;
        try {
          const payload = {
            id: this.dialogForm.id,
            name: this.dialogForm.name,
            description: this.dialogForm.description,
            // join 为英文逗号
            questionTypes: (this.dialogForm.questionTypesArr || []).join(',')
          };

          if (this.isEdit) {
            // 编辑不需要模型参数
            await this.$axios.post('/api/testset/update', payload);
            this.$message.success('更新成功');
          } else {
            // 新增：拼上模型参数列表
            const recordModelSettingEntityList = this.buildRecordModelSettingList();
            const reqBody = { ...payload, recordModelSettingEntityList };
            await this.$axios.post('/api/testset/add', reqBody);
            this.$message.success('新增成功');
          }

          this.dialogVisible = false;
          this.loadData();
        } finally {
          this.dialogLoading = false;
        }
      });
    },

    async onDelete(id) {
      await this.$confirm('确认删除该测试集？', '警告', { type: 'warning' });
      await this.$axios.delete(`/api/testset/${id}`);
      this.$message.success('删除成功');
      this.loadData();
    },

    openRecordModelSetting(testSetId) {
      this.$refs.recordModelSettingDialog?.show({ testSetId });
    }
  }
};
</script>

<style lang="scss" scoped>
.mr-6 { margin-right: 6px; }

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.el-form-item__extra {
  margin-left: 12px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}
</style>
