<template>
  <el-dialog
      v-model="visible"
      title="详情信息"
      width="600px"
      @close="onClose"
  >
    <div class="detail-content">
      <p><span class="label">模型名称：</span><span class="value">
        <el-link type="primary" @click="showModelDetail(data.fileId)">点击查看</el-link>

        <el-icon class="copy-icon" @click="copyText(data.modelLabel)"><CopyDocument /></el-icon></span></p>
      <p>
        <span class="label">修改数量/总数量：</span>
        <span class="value">{{ data.modifiedCount }}/{{ data.totalCount }}<el-icon class="copy-icon" @click="copyText(data.modifiedCount + '/' + data.totalCount)"><CopyDocument /></el-icon></span>
        <el-tooltip placement="top" content="准确率1(批改失败视为本题批改结果为正确) 准确率2(批改失败视为未作答)">
          <span class="highlight">（准确率1：{{ data.accuracy1 }}% 准确率2：{{ data.accuracy2 }}%）<el-icon class="copy-icon" @click="copyText('准确率1：' + data.accuracy1 + '% 准确率2：' + data.accuracy2 + '%')"><CopyDocument /></el-icon></span>
        </el-tooltip>
      </p>
      <p>
        <span class="label">批改失败数量/总数量：</span>
        <span class="value">{{ data.failCount }}/{{ data.totalCount }}<el-icon class="copy-icon" @click="copyText(data.failCount + '/' + data.totalCount)"><CopyDocument /></el-icon></span>
        <span class="highlight">（占比：{{ data.failCountPercentage }}%<el-icon class="copy-icon" @click="copyText(data.failCountPercentage + '%')"><CopyDocument /></el-icon>）</span>
      </p>
      <p><span class="label">纠错时间：</span><span class="value">{{ data.timesStr }}<el-icon class="copy-icon" @click="copyText(data.timesStr)"><CopyDocument /></el-icon></span></p>
      <p v-if="data.durationText"><span class="label">批改总时长：</span><span class="value">{{ data.durationText }}<el-icon class="copy-icon" @click="copyText(data.durationText)"><CopyDocument /></el-icon><span class="tip">（Tip：从开始批改到结束的时间，并非每个请求的耗时之和。受同时批改试卷数量的影响）</span></span></p>
      <p v-if="data.stats"><span class="label">平均时长：</span><span class="value">{{ (data.stats?.averageTimeConsumption || 0.00).toFixed(2) }}s<el-icon class="copy-icon" @click="copyText((data.stats?.averageTimeConsumption || 0.00).toFixed(2) + 's')"><CopyDocument /></el-icon></span></p>
      <p v-if="data.stats"><span class="label">总时长：</span><span class="value">{{ data.stats?.totalTimeConsumption }}s / {{data.stats?.totalCount}}个<el-icon class="copy-icon" @click="copyText(data.stats?.totalTimeConsumption + 's / ' + data.stats?.totalCount + '个')"><CopyDocument /></el-icon></span></p>
      <p v-if="data.stats?.nullCount"><span class="label">无时间记录的数量：</span><span class="value">{{ data.stats?.nullCount }}个<el-icon class="copy-icon" @click="copyText(data.stats?.nullCount + '个')"><CopyDocument /></el-icon></span></p>
      <p ><span class="label">平均分：</span><span class="value">{{ data.averageScore }} / {{data.onePaperTotalScore}} (分)<el-icon class="copy-icon" @click="copyText(data.averageScore + ' / ' + data.onePaperTotalScore + ' (分)')"><CopyDocument /></el-icon></span></p>
      <p ><span class="label">中位数：</span><span class="value">{{ data.medianScore }} / {{data.onePaperTotalScore}} (分)<el-icon class="copy-icon" @click="copyText(data.medianScore + ' / ' + data.onePaperTotalScore + ' (分)')"><CopyDocument /></el-icon></span></p>
      <p ><span class="label">总分之和：</span><span class="value">{{ data.totalScoreSum }} 分/ {{data.studentCount}} 人<el-icon class="copy-icon" @click="copyText(data.totalScoreSum + '分/ ' + data.studentCount + '人')"><CopyDocument /></el-icon></span></p>
    </div>
    <template #footer>
      <el-button
        class="close-button"
        @click="onClose"
        style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave">
        关闭
      </el-button>
    </template>
    <recordModelSettingDialog ref="recordModelSettingDialog"></recordModelSettingDialog>
  </el-dialog>
</template>

<script>
import { CopyDocument } from '@element-plus/icons-vue';
import {useUserStore} from "../../../store";
import recordModelSettingDialog from "@/views/ModelRequest/recordModelSettingDialog.vue";

const store = useUserStore();
export default {
  name: 'DetailDialog',
  components: {recordModelSettingDialog, CopyDocument},
  data() {
    return {
      visible: false,
      data: {
        modelLabel: '',
        modifiedCount: 0,
        totalCount: 0,
        accuracy: '0.00',
        timesStr: '',
        durationText: '',
        failCount: 0,
        failCountPercentage: '0.00',
        totalScoreSum: 0,
        studentCount: 0,
        averageScore: 0,
        medianScore: 0,
        onePaperTotalScore: 0,
      }
    };
  },
  methods: {
    showModelDetail(id) {
      this.$refs.recordModelSettingDialog.show({fileId: id});
    },
    onClose() {
      this.visible = false;
    },
    show(data) {
      this.data = data;
      this.visible = true;
    },
    copyText(text) {
      if (!text && text !== 0) return;
      navigator.clipboard.writeText(String(text)).then(() => {
        this.$message.success('已复制');
      }).catch(() => {
        this.$message.error('复制失败，请手动复制');
      });
    },
    handleMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    }
  }
}
</script>

<style scoped>
/* 整体字体放大 */
.detail-content {
  font-size: 16px;
  line-height: 1.8;
}

/* 每行间距增大 */
.detail-content p {
  margin: 12px 0;
}

/* 标签部分加粗 */
.detail-content .label {
  font-weight: 600;
  font-size: 17px;
}

/* 数值部分稍弱一些对比 */
.detail-content .value {
  font-weight: 400;
}

/* 准确率等重点信息着重显示 */
.detail-content .highlight {
  font-weight: 700;
  font-size: 17px;
  color: #409EFF; /* Element Plus 主色调 */
}

.copy-icon {
  cursor: pointer;
  margin-left: 6px;
  font-size: 16px;
  color: #409EFF;
  vertical-align: middle;
}

.detail-content .tip {
  margin-left: 6px;
}

/* 关闭按钮自定义样式 */
:deep(.el-dialog__footer .close-button),
:deep(.close-button.el-button),
:deep(.close-button) {
  background-color: #1677FF !important;
  color: #FFF !important;
  border: 1px solid #1677FF !important;
  font-weight: 700 !important;
  transition: all 0.3s ease !important;

  &:hover {
    background-color: #4a90ff !important;
    color: #FFF !important;
    border-color: #4a90ff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3) !important;
  }

  &:focus {
    background-color: #4a90ff !important;
    color: #FFF !important;
    border-color: #4a90ff !important;
  }

  &:active {
    background-color: #1677FF !important;
    color: #FFF !important;
    border-color: #1677FF !important;
  }

  /* 确保覆盖所有可能的默认状态 */
  &.el-button--default {
    background-color: #1677FF !important;
    color: #FFF !important;
    border-color: #1677FF !important;
  }
}
</style>
