<template>
  <el-dialog
      v-model="visible"
      title="批改结果"
      width="1100px"
      append-to-body
      destroy-on-close
  >
    <!-- 查询区 -->
    <el-form inline :model="search" class="header-form" style="margin-bottom: 8px;">
      <el-form-item label="测试集ID">
        <el-input
            v-model="search.testSetId"
            placeholder="请输入测试集ID"
            clearable
            style="width: 220px;"
            :disabled="lockedByShow"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="search.status" placeholder="全部" clearable style="width: 160px;">
          <el-option label="批改中" value="PROCESSING" />
          <el-option label="批改完成" value="DONE" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="loadData">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%;"
        empty-text="无数据"
    >
      <el-table-column type="index" label="序号" width="70" />

      <el-table-column label="测试集快照" min-width="260">
        <template #default="{ row }">
          <div class="ellipsis">
            <strong>{{ row.testSetName || '-' }}</strong>
          </div>
          <el-tooltip :content="row.testSetDescription || ''" placement="top" v-if="row.testSetDescription">
            <div class="desc muted ellipsis">{{ row.testSetDescription }}</div>
          </el-tooltip>
          <div class="qtypes">
            <el-tag
                v-for="(q, idx) in splitTypes(row.testSetQuestionTypes)"
                :key="idx"
                size="small"
                class="mr-6"
            >{{ q }}</el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="答题统计" width="220">
        <template #default="{ row }">
          <div>正确/总数：{{ row.correctCount ?? 0 }}/{{ row.totalCount ?? 0 }}
            <el-tag size="small" type="success" v-if="row.totalCount">
              {{ percent(row.correctCount, row.totalCount) }}
            </el-tag>
          </div>
          <div class="muted">图片成功/总数：{{ row.imageSuccessCount ?? 0 }}/{{ row.imageCount ?? 0 }}</div>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="row.status === 'DONE' ? 'success' : 'warning'">
            {{ row.status === 'DONE' ? '批改完成' : '批改中' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="报告" width="150">
        <template #default="{ row }">
          <el-button
              v-if="row.mdUrl"
              type="primary"
              link
              @click="openMd(row.mdUrl)"
          >查看报告</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" width="170" />
      <el-table-column prop="updateTime" label="更新时间" width="170" />

      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template #default="{ row }">
          <el-link type="primary" @click="openMd(row.mdUrl)" :disabled="!row.mdUrl">查看报告</el-link>
          <el-divider direction="vertical" />
          <!-- ✅ 批改日志 -->
          <el-link type="info" @click="openLogs(row)">批改日志</el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="display:flex; justify-content:flex-end; margin-top: 10px;">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <template #footer>
      <el-button @click="visible=false">关闭</el-button>
    </template>

    <!-- 批改日志 Dialog -->
    <el-dialog
        v-model="logDialog.visible"
        title="批改日志"
        width="800px"
        append-to-body
    >
      <div v-if="logDialog.loading" style="text-align:center; padding: 40px 0;">
        <el-icon class="is-loading"><i-ep-loading /></el-icon>
      </div>

      <template v-else>
        <!-- 若服务端返回数组 {time, level, message} -->
        <el-table
            v-if="Array.isArray(logDialog.data)"
            :data="logDialog.data"
            border
            size="small"
        >
          <el-table-column prop="time" label="时间" width="180" />
          <el-table-column prop="level" label="级别" width="100" />
          <el-table-column prop="message" label="内容" min-width="360">
            <template #default="{ row }">
              <pre class="log-pre">{{ row.message }}</pre>
            </template>
          </el-table-column>
          <el-table-column prop="operation" label="操作" min-width="360">
            <template #default="{ row }">
              <el-link
                  type="info"
                  @click="$router.push({ name: 'TestSetImageResultManage', query: { testSetId: row.id } })"
              >
                查看图片批改结果
              </el-link>
            </template>
          </el-table-column>
        </el-table>

        <!-- 若服务端返回字符串 -->
        <pre v-else class="log-pre">{{ logDialog.data || '（无日志）' }}</pre>
      </template>

      <template #footer>
        <el-button @click="logDialog.visible=false">关闭</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  name: 'TestSetResultDialog',
  data() {
    return {
      visible: false,
      lockedByShow: false, // show({testSetId}) 时锁定测试集ID输入
      loading: false,
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      search: {
        testSetId: '',
        status: ''
      },
      logDialog: {
        visible: false,
        loading: false,
        resultId: null,
        data: null
      }
    };
  },
  methods: {
    /** 对外暴露：打开对话框（可指定 testSetId） */
    show({ testSetId } = {}) {
      this.visible = true;
      this.pageNumber = 1;
      if (testSetId != null) {
        this.search.testSetId = String(testSetId);
        this.lockedByShow = true;
      } else {
        this.lockedByShow = false;
      }
      this.loadData();
    },

    splitTypes(str) {
      if (!str) return [];
      return String(str).split(',').map(s => s.trim()).filter(Boolean);
    },
    percent(ok, all) {
      const a = Number(ok || 0);
      const b = Number(all || 0);
      if (!b) return '0%';
      return ((a * 100) / b).toFixed(1) + '%';
    },

    async loadData() {
      this.loading = true;
      try {
        const params = {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
          testSetId: this.search.testSetId || undefined,
          status: this.search.status || undefined
        };
        const res = await this.$axios.get('/api/testsetResult/page', { params });
        const pageObj = res?.data?.data ?? res?.data ?? {};
        this.tableData = pageObj.records || [];
        this.total = pageObj.total || 0;
        this.pageSize = pageObj.size || this.pageSize;
        this.pageNumber = pageObj.current || this.pageNumber;
      } finally {
        this.loading = false;
      }
    },

    reset() {
      // 如果通过 show 传入了 testSetId，则只重置 status
      if (this.lockedByShow) {
        this.search.status = '';
        this.pageNumber = 1;
      } else {
        this.search = { testSetId: '', status: '' };
        this.pageNumber = 1;
      }
      this.loadData();
    },

    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },

    openMd(url) {
      if (!url) return;
      window.open(url, '_blank');
    },

    /** 打开批改日志 */
    async openLogs(row) {
      this.logDialog.visible = true;
      this.logDialog.loading = true;
      this.logDialog.resultId = row.id;
      try {
        // ⚠️ 调整为你的实际日志接口：
        // 优先 /api/testsetResult/logs?resultId=xxx
        // 若没有该接口，可改为 /api/testsetResult/{id}/logs
        let data = null;
        try {
          const res = await this.$axios.get('/api/testsetResult/logs', { params: { resultId: row.id } });
          data = res?.data?.data ?? res?.data ?? null;
        } catch (e) {
          const res2 = await this.$axios.get(`/api/testsetResult/${row.id}/logs`);
          data = res2?.data?.data ?? res2?.data ?? null;
        }
        this.logDialog.data = data;
      } catch (e) {
        this.logDialog.data = '（拉取日志失败）';
      } finally {
        this.logDialog.loading = false;
      }
    }
  },
  watch: {
    visible(v) {
      if (!v) {
        // 关闭时清理日志弹窗状态
        this.logDialog = { visible: false, loading: false, resultId: null, data: null };
      }
    }
  }
};
</script>

<style scoped lang="scss">
.header-form {
  :deep(.el-form-item) {
    margin-bottom: 8px;
  }
}
.mr-6 { margin-right: 6px; }
.muted { color: var(--el-text-color-secondary); }
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.desc { max-width: 420px; }
.qtypes { margin-top: 4px; }
.log-pre {
  background: #0f172a;
  color: #e2e8f0;
  border-radius: 6px;
  padding: 10px;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
