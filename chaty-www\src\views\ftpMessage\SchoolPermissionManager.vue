<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-text class="title">学校权限管理</el-text>
      </template>
    </el-page-header>

    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- ================= Tab 1：按学校管理 ================= -->
      <el-tab-pane label="按学校管理" name="byTitle">
        <div class="toolbar">
          <el-form inline :model="titleSearch" ref="titleSearchRef" class="header-form">
            <el-form-item label="学校名称">
              <el-select
                  v-model="titleSearch.selectedTitle"
                  value-key="id"
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="输入学校名称搜索"
                  :remote-method="remoteSearchTitles"
                  :loading="titleLoading"
                  style="width: 320px;"
                  @change="onTitleChange"
              >
                <el-option
                    v-for="item in titleOptions"
                    :key="item.id"
                    :label="item.title"
                    :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                class="query-button-title"
                :loading="mapLoading"
                @click="loadMappingsByTitle"
                style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
                @mouseenter="handleQueryTitleMouseEnter"
                @mouseleave="handleQueryTitleMouseLeave">
                查询
              </el-button>
              <el-button @click="resetTitleSearch">重置</el-button>
              <el-button type="success" :disabled="!titleSearch.selectedTitle" @click="openUserDrawer">添加授权
              </el-button>
              <el-button type="danger" :disabled="!multipleSelection.length" @click="batchRevoke">批量移除</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
            v-loading="mapLoading"
            :data="tableData"
            style="width: 100%;"
            :empty-text="titleEmptyText"
            @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="60"/>
          <el-table-column type="index" label="序号" width="80"/>
          <el-table-column label="用户名" width="220">
            <template #default="{ row }">{{ row.user?.username || '-' }}</template>
          </el-table-column>
          <el-table-column label="昵称" width="180">
            <template #default="{ row }">{{ row.user?.nickname || '-' }}</template>
          </el-table-column>
          <el-table-column label="学号" width="160">
            <template #default="{ row }">{{ row.user?.studentId || '-' }}</template>
          </el-table-column>
          <el-table-column label="默认学校" width="220">
            <template #default="{ row }">{{ row.user?.defaultSchool || '-' }}</template>
          </el-table-column>
          <el-table-column prop="createTime" label="授权时间" width="180"/>
          <el-table-column label="操作" fixed="right" width="160">
            <template #default="{ row }">
              <el-link type="danger" @click="revoke(row)">移除授权</el-link>
            </template>
          </el-table-column>
        </el-table>

        <div class="footer-bar">
          <el-pagination
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :current-page="pageNumber"
              :total="total"
              @current-change="handlePageChange"
          />
        </div>

        <!-- 抽屉：选择用户批量授权给当前学校 -->
        <el-drawer v-model="userDrawerVisible" :title="`添加授权 - ${titleSearch.selectedTitle?.title || ''}`"
                   size="70%">
          <div style="margin-bottom: 12px;">
            <el-form inline :model="userSearch" ref="userSearchRef">
              <el-form-item label="用户名">
                <el-input v-model="userSearch.username" placeholder="用户名" clearable style="width: 200px;"/>
              </el-form-item>
              <el-form-item label="昵称">
                <el-input v-model="userSearch.nickname" placeholder="昵称" clearable style="width: 200px;"/>
              </el-form-item>
              <el-form-item label="学号">
                <el-input v-model="userSearch.studentId" placeholder="学号" clearable style="width: 200px;"/>
              </el-form-item>
              <el-form-item>
                <el-button
                  class="query-button-user"
                  :loading="userLoading"
                  @click="loadUsers"
                  style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
                  @mouseenter="handleQueryUserMouseEnter"
                  @mouseleave="handleQueryUserMouseLeave">
                  查询
                </el-button>
                <el-button @click="resetUserSearch">重置</el-button>
                <el-button type="success" :disabled="!userSelection.length" @click="grantSelectedToTitle">批量授权
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table
              v-loading="userLoading"
              :data="userTable"
              style="width: 100%;"
              empty-text="无用户"
              @selection-change="handleUserSelectionChange"
          >
            <el-table-column type="selection" width="60"/>
            <el-table-column type="index" label="序号" width="70"/>
            <el-table-column prop="username" label="用户名" width="240"/>
            <el-table-column prop="nickname" label="昵称" width="180"/>
            <el-table-column prop="studentId" label="学号" width="160"/>
            <el-table-column prop="defaultSchool" label="默认学校" width="220"/>
            <el-table-column label="操作" fixed="right" width="150">
              <template #default="{ row }">
                <el-link type="primary" @click="grantOneToTitle(row)">授予</el-link>
              </template>
            </el-table-column>
          </el-table>

          <div style="display:flex; justify-content:flex-end; margin-top: 12px;">
            <el-pagination
                background
                layout="prev, pager, next"
                :page-size="userPageSize"
                :current-page="userPageNo"
                :total="userTotal"
                @current-change="handleUserPageChange"
            />
          </div>
        </el-drawer>
      </el-tab-pane>

      <!-- ================= Tab 2：按用户管理 ================= -->
      <el-tab-pane label="按用户管理" name="byUser">
        <div class="toolbar">
          <el-form inline :model="userPick" ref="userPickRef" class="header-form">
            <el-form-item label="选择用户">
              <el-select
                  v-model="userPick.selectedUser"
                  filterable
                  remote
                  reserve-keyword
                  clearable
                  placeholder="输入用户名/昵称/学号搜索"
                  :remote-method="remoteSearchUsers"
                  :loading="userPickLoading"
                  style="width: 360px;"
                  @change="onUserPickChange"
              >
                <el-option
                    v-for="u in userPickOptions"
                    :key="u.id"
                    :label="userLabel(u)"
                    :value="u"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                class="query-button-user-tab"
                :loading="mapLoading"
                @click="loadMappingsByUser"
                style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
                @mouseenter="handleQueryUserTabMouseEnter"
                @mouseleave="handleQueryUserTabMouseLeave">
                查询
              </el-button>
              <el-button @click="resetUserPick">重置</el-button>
              <el-button type="success" :disabled="!userPick.selectedUser" @click="openTitleDrawer">添加授权</el-button>
              <el-button type="danger" :disabled="!multipleSelection.length" @click="batchRevoke">批量移除</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
            v-loading="mapLoading"
            :data="tableData"
            style="width: 100%;"
            :empty-text="userEmptyText"
            @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="60"/>
          <el-table-column type="index" label="序号" width="80"/>
          <el-table-column label="学校名称" width="280">
            <template #default="{ row }">{{ row.title?.title || `#${row.titleId}` }}</template>
          </el-table-column>
          <el-table-column label="路径" width="240">
            <template #default="{ row }">{{ row.title?.path || '-' }}</template>
          </el-table-column>
          <el-table-column label="权重" width="120">
            <template #default="{ row }">{{ row.title?.weight ?? '-' }}</template>
          </el-table-column>
          <el-table-column label="备注">
            <template #default="{ row }">
              <el-tooltip placement="top" :content="row.title?.remark || ''">
                <span>{{ brief(row.title?.remark) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="授权时间" width="180"/>
          <el-table-column label="操作" fixed="right" width="160">
            <template #default="{ row }">
              <el-link type="danger" @click="revoke(row)">移除授权</el-link>
            </template>
          </el-table-column>
        </el-table>

        <div class="footer-bar">
          <el-pagination
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :current-page="pageNumber"
              :total="total"
              @current-change="handlePageChange"
          />
        </div>

        <!-- 抽屉：选择学校批量授权给当前用户 -->
        <el-drawer v-model="titleDrawerVisible"
                   :title="`添加授权 - ${userPick.selectedUser ? userLabel(userPick.selectedUser) : ''}`" size="70%">
          <div style="margin-bottom: 12px;">
            <el-form inline :model="titlePickSearch" ref="titlePickSearchRef">
              <el-form-item label="学校名称">
                <el-input v-model="titlePickSearch.title" placeholder="学校名称" clearable style="width: 220px;"/>
              </el-form-item>
              <el-form-item label="备注">
                <el-input v-model="titlePickSearch.remark" placeholder="备注" clearable style="width: 220px;"/>
              </el-form-item>
              <el-form-item>
                <el-button
                  class="query-button-title-drawer"
                  :loading="titleTableLoading"
                  @click="loadTitleTable"
                  style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
                  @mouseenter="handleQueryTitleDrawerMouseEnter"
                  @mouseleave="handleQueryTitleDrawerMouseLeave">
                  查询
                </el-button>
                <el-button @click="resetTitlePickSearch">重置</el-button>
                <el-button type="success" :disabled="!titleSelection.length" @click="grantSelectedToUser">批量授权
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table
              v-loading="titleTableLoading"
              :data="titleTable"
              style="width: 100%;"
              empty-text="无学校"
              @selection-change="handleTitleSelectionChange"
          >
            <el-table-column type="selection" width="60"/>
            <el-table-column type="index" label="序号" width="70"/>
            <el-table-column prop="title" label="学校名称" width="260"/>
            <el-table-column prop="path" label="路径" width="220"/>
            <el-table-column prop="weight" label="权重" width="120"/>
            <el-table-column prop="remark" label="备注"/>
            <el-table-column label="操作" fixed="right" width="150">
              <template #default="{ row }">
                <el-link type="primary" @click="grantOneToUser(row)">授予</el-link>
              </template>
            </el-table-column>
          </el-table>

          <div style="display:flex; justify-content:flex-end; margin-top: 12px;">
            <el-pagination
                background
                layout="prev, pager, next"
                :page-size="titlePageSize"
                :current-page="titlePageNo"
                :total="titleTotal"
                @current-change="handleTitlePageChange"
            />
          </div>
        </el-drawer>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'SchoolPermissionManager',
  data() {
    return {
      activeTab: 'byTitle',

      // ===== 按学校检索 =====
      titleSearch: {selectedTitle: null},
      titleOptions: [],
      titleLoading: false,

      // ===== 按用户检索（顶部远程select）=====
      userPick: {selectedUser: null},
      userPickOptions: [],
      userPickLoading: false,

      // ===== 映射列表（公用）=====
      tableData: [],
      mapLoading: false,
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      multipleSelection: [],

      // ===== 抽屉：从用户列表里选，授权给学校 =====
      userDrawerVisible: false,
      userSearch: {username: '', nickname: '', studentId: ''},
      userLoading: false,
      userTable: [],
      userSelection: [],
      userPageNo: 1,
      userPageSize: 10,
      userTotal: 0,

      // ===== 抽屉：从学校列表里选，授权给用户 =====
      titleDrawerVisible: false,
      titlePickSearch: {title: '', remark: ''},
      titleTableLoading: false,
      titleTable: [],
      titleSelection: [],
      titlePageNo: 1,
      titlePageSize: 10,
      titleTotal: 0,

      // 详情缓存
      userCache: new Map(),   // userId -> user
      titleCache: new Map(),  // titleId -> title
    };
  },
  computed: {
    titleEmptyText() {
      return this.titleSearch.selectedTitle ? '暂无授权用户' : '请选择学校后查询';
    },
    userEmptyText() {
      return this.userPick.selectedUser ? '暂无授权学校' : '请选择用户后查询';
    }
  },
  methods: {
    goBack() {
      this.$router.back();
    },
    handleTabChange() {
      // 清空公用状态
      this.tableData = [];
      this.multipleSelection = [];
      this.pageNumber = 1;
      this.total = 0;
    },
    brief(txt) {
      if (!txt) return '-';
      return String(txt).length > 30 ? String(txt).slice(0, 30) + '...' : String(txt);
    },
    userLabel(u) {
      // 下拉显示：昵称(用户名)/学号
      const left = u.nickname ? `${u.nickname}` : (u.username || '');
      const right = u.studentId ? `/${u.studentId}` : '';
      return `${left}${right}`;
    },

    /* ---------------- 按学校：选择与查询 ---------------- */
    remoteSearchTitles(query) {
      this.titleLoading = true;
      this.$axios.post('/api/ftpMessageTitle/select', {
        page: {pageNumber: 1, pageSize: 20, searchCount: true},
        title: query || ''
      }).then(res => {
        const d = res.data || {};
        this.titleOptions = d.records || [];
      }).finally(() => {
        this.titleLoading = false;
      });
    },
    onTitleChange() {
      this.pageNumber = 1;
      // 不提示、不报错；用户点“查询”时再加载
    },
    resetTitleSearch() {
      this.titleSearch.selectedTitle = null;
      this.pageNumber = 1;
      this.total = 0;
      this.tableData = [];
      this.multipleSelection = [];
    },
    loadMappingsByTitle() {
      if (!this.titleSearch.selectedTitle) {
        // 未选择时不报错，仅清空
        this.tableData = [];
        this.total = 0;
        return;
      }
      this.mapLoading = true;
      const titleId = this.titleSearch.selectedTitle.id;
      this.$axios.post('/api/ftpMessageUserTitle/list', null, {
        params: {titleId, pageNo: this.pageNumber, pageSize: this.pageSize}
      }).then(async res => {
        const d = res.data || {};
        const records = d.records || [];
        this.total = d.total || 0;
        console.log('records', records)
        // 补充用户详情（空结果不会报错）
        const enriched = await Promise.all(records.map(async r => {
          const user = await this.fetchUser(r.userId);
          return {...r, user};
        }));
        this.tableData = enriched;
      }).finally(() => {
        this.mapLoading = false;
      });
    },

    /* ---------------- 按用户：选择与查询 ---------------- */
    remoteSearchUsers(query) {
      this.userPickLoading = true;
      this.$axios.post('/api/user/page', {
        page: {pageNumber: 1, pageSize: 20, searchCount: true},
        username: query || '',
        nickname: query || '',
        studentId: query || ''
      }).then(res => {
        const d = res.data || {};
        this.userPickOptions = d.records || [];
      }).finally(() => {
        this.userPickLoading = false;
      });
    },
    onUserPickChange() {
      this.pageNumber = 1;
      // 等用户点击“查询”
    },
    resetUserPick() {
      this.userPick.selectedUser = null;
      this.pageNumber = 1;
      this.total = 0;
      this.tableData = [];
      this.multipleSelection = [];
    },
    loadMappingsByUser() {
      if (!this.userPick.selectedUser) {
        this.tableData = [];
        this.total = 0;
        return;
      }
      this.mapLoading = true;
      const userId = this.userPick.selectedUser.id;
      this.$axios.post('/api/ftpMessageUserTitle/list', null, {
        params: {userId, pageNo: this.pageNumber, pageSize: this.pageSize}
      }).then(async res => {
        const d = res.data || {};
        const records = d.records || [];
        this.total = d.total || 0;

        // 补充学校详情
        const enriched = await Promise.all(records.map(async r => {
          const title = await this.fetchTitle(r.titleId);
          return {...r, title};
        }));
        this.tableData = enriched;
      }).finally(() => {
        this.mapLoading = false;
      });
    },

    /* ---------------- 公用：分页/选择/删除 ---------------- */
    handlePageChange(page) {
      this.pageNumber = page;
      if (this.activeTab === 'byTitle') this.loadMappingsByTitle();
      else this.loadMappingsByUser();
    },
    handleSelectionChange(rows) {
      this.multipleSelection = rows;
    },
    revoke(row) {
      this.$confirm('确认移除该授权？', '警告', {type: 'warning'})
          .then(() => this.$axios.post('/api/ftpMessageUserTitle/delete', null, {params: {id: row.id}}))
          .then(() => {
            this.$message.success('已移除授权');
            if (this.activeTab === 'byTitle') this.loadMappingsByTitle();
            else this.loadMappingsByUser();
          })
          .catch(() => {
          });
    },
    batchRevoke() {
      const ids = this.multipleSelection.map(r => r.id);
      if (!ids.length) return;
      this.$confirm(`确认批量移除选中的 ${ids.length} 条授权？`, '警告', {type: 'warning'})
          .then(() => this.$axios.post('/api/ftpMessageUserTitle/deleteBatch', ids))
          .then(() => {
            this.$message.success('批量移除成功');
            if (this.activeTab === 'byTitle') this.loadMappingsByTitle();
            else this.loadMappingsByUser();
          })
          .catch(() => {
          });
    },

    /* ---------------- 抽屉①：给学校添加用户授权 ---------------- */
    openUserDrawer() {
      if (!this.titleSearch.selectedTitle) return;
      this.userDrawerVisible = true;
      this.userSelection = [];
      this.userPageNo = 1;
      this.loadUsers();
    },
    loadUsers() {
      this.userLoading = true;
      const payload = {
        page: {pageNumber: this.userPageNo, pageSize: this.userPageSize, searchCount: true},
        username: this.userSearch.username || undefined,
        nickname: this.userSearch.nickname || undefined,
        studentId: this.userSearch.studentId || undefined
      };
      this.$axios.post('/api/user/page', payload)
          .then(res => {
            const d = res.data || {};
            this.userTable = d.records || [];
            this.userTotal = d.total || 0;
          })
          .finally(() => {
            this.userLoading = false;
          });
    },
    handleUserPageChange(page) {
      this.userPageNo = page;
      this.loadUsers();
    },
    resetUserSearch() {
      this.userSearch = {username: '', nickname: '', studentId: ''};
      this.userPageNo = 1;
      this.loadUsers();
    },
    handleUserSelectionChange(rows) {
      this.userSelection = rows;
    },
    async grantOneToTitle(userRow) {
      if (!this.titleSearch.selectedTitle) return;
      const titleId = this.titleSearch.selectedTitle.id;
      await this.$axios.post('/api/ftpMessageUserTitle/create', {userId: userRow.id, titleId});
      this.$message.success('授权成功');
      this.loadMappingsByTitle();
    },
    async grantSelectedToTitle() {
      if (!this.titleSearch.selectedTitle || !this.userSelection.length) return;
      const titleId = this.titleSearch.selectedTitle.id;
      await Promise.all(this.userSelection.map(u => this.$axios.post('/api/ftpMessageUserTitle/create', {
        userId: u.id,
        titleId
      })));
      this.$message.success('批量授权成功');
      this.loadMappingsByTitle();
    },

    /* ---------------- 抽屉②：给用户添加学校授权 ---------------- */
    openTitleDrawer() {
      if (!this.userPick.selectedUser) return;
      this.titleDrawerVisible = true;
      this.titleSelection = [];
      this.titlePageNo = 1;
      this.loadTitleTable();
    },
    loadTitleTable() {
      this.titleTableLoading = true;
      this.$axios.post('/api/ftpMessageTitle/selectName', {
        page: {pageNumber: this.titlePageNo, pageSize: this.titlePageSize, searchCount: true},
        title: this.titlePickSearch.title || '',
        remark: this.titlePickSearch.remark || ''
      }).then(res => {
        const d = res.data || {};
        this.titleTable = d.records || [];
        this.titleTotal = d.total || 0;
      }).finally(() => {
        this.titleTableLoading = false;
      });
    },
    handleTitlePageChange(page) {
      this.titlePageNo = page;
      this.loadTitleTable();
    },
    resetTitlePickSearch() {
      this.titlePickSearch = {title: '', remark: ''};
      this.titlePageNo = 1;
      this.loadTitleTable();
    },
    handleTitleSelectionChange(rows) {
      this.titleSelection = rows;
    },
    async grantOneToUser(titleRow) {
      if (!this.userPick.selectedUser) return;
      const userId = this.userPick.selectedUser.id;
      await this.$axios.post('/api/ftpMessageUserTitle/create', {userId, titleId: titleRow.id});
      this.$message.success('授权成功');
      this.loadMappingsByUser();
    },
    async grantSelectedToUser() {
      if (!this.userPick.selectedUser || !this.titleSelection.length) return;
      const userId = this.userPick.selectedUser.id;
      await Promise.all(this.titleSelection.map(t => this.$axios.post('/api/ftpMessageUserTitle/create', {
        userId,
        titleId: t.id
      })));
      this.$message.success('批量授权成功');
      this.loadMappingsByUser();
    },

    /* ---------------- 详情缓存（避免重复请求） ---------------- */
    async fetchUser(userId) {
      if (!userId) return null;
      if (this.userCache.has(userId)) return this.userCache.get(userId);
      try {
        const res = await this.$axios.get('/api/user/findById', {params: {id: userId}});
        const user = res.data || null;
        this.userCache.set(userId, user);
        return user;
      } catch {
        return null;
      }
    },
    async fetchTitle(titleId) {
      if (!titleId) return null;
      if (this.titleCache.has(titleId)) return this.titleCache.get(titleId);
      try {
        // 期望后端支持按 id 精确查询；如不支持会退化
        const res = await this.$axios.post('/api/ftpMessageTitle/select', {
          page: {pageNumber: 1, pageSize: 1, searchCount: false},
          id: titleId
        });
        const recs = (res.data && res.data.records) || [];
        const title = recs[0] || null;
        this.titleCache.set(titleId, title);
        return title;
      } catch {
        return null;
      }
    },
    // 按学校管理标签页的查询按钮
    handleQueryTitleMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleQueryTitleMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    // 用户抽屉中的查询按钮
    handleQueryUserMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleQueryUserMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    // 按用户管理标签页的查询按钮
    handleQueryUserTabMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleQueryUserTabMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    // 学校抽屉中的查询按钮
    handleQueryTitleDrawerMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleQueryTitleDrawerMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    }
  },
  created() {
    // 预加载一点学校列表，便于首次选择
    this.remoteSearchTitles('');
  }
};
</script>

<style lang="scss" scoped>
.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    height: 48px;
    flex-shrink: 0;

    .title {
      font-weight: 600;
      font-size: 16px;
    }
  }

  .toolbar {
    margin-bottom: 12px;
  }

  .footer-bar {
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
