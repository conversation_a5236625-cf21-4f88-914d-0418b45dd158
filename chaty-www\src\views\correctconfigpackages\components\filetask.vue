<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-image src="/icon/16.png" class="icon"></el-image>
          <div @click="onEditName(configPackages?.id, configPackages?.name)" class="text"
               style="text-decoration: underline;cursor: pointer;">{{ configPackages?.name }}
          </div>

          <el-tooltip placement="top" content="添加一个标准卷">
            <el-button icon="Plus" link style="margin-left: 10px;margin-top: 3px;font-size: 18px;"
                       @click="$refs.configPaperSelect.show()">添加
            </el-button>
          </el-tooltip>

          <!--          <el-image src="/icon/correctPackageStep2.svg" class="step"></el-image>-->
        </div>
      </template>
    </el-page-header>

    <div class="main-content">
      <div class="left-content">
        <el-empty description="加载中..." v-if="configIdsDetail.length === 0"/>
        <el-table v-else :data="configIdsDetail" style="height: 100%;table-layout: fixed;" empty-text="无数据"
                  :border="false" row-key="id">
          <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center"
                           :fixed="column.prop === 'operations' ? 'right':''">
            <template v-if="column.prop === 'operations'" v-slot="scope">
              <el-space :size="5">
                <el-button type="primary" text size="small" @click="onLook(scope.row.id)">开始配置</el-button>
                <el-dropdown>
                  <span>
                    <el-icon class="el-icon--right">
                      <more/>
                    </el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="$refs.configPaperSelect.show(scope.$index)">编辑
                      </el-dropdown-item>
                      <el-dropdown-item @click="onEditName(scope.row.id, scope.row.name)">编辑姓名
                      </el-dropdown-item>
                      <el-dropdown-item @click="onDeleteTask(scope.row.id)" divided>删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-space>
            </template>
            <template v-else-if="column.prop === 'areas' && column.label === '区域'" v-slot="scope">
              {{ JSON.parse(scope.row.areas).length }}个区域
            </template>
            <template v-else-if="column.prop === 'areas' && column.label === '分数'" v-slot="scope">
              <el-text class="score">{{ calculateTotalScore(JSON.parse(scope.row.areas)) }} / {{
                  allPaperTotalScore
                }}
              </el-text>
            </template>
            <template v-else-if="column.prop === 'createTime'" v-slot="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
            <template v-else-if="column.prop === 'name'" v-slot="scope">
              <el-link
                class="name-link"
                type="primary"
                :underline="false"
                @click="onLook(scope.row.id)"
              >
                {{ scope.row.name }}
              </el-link>
            </template>
          </el-table-column>
        </el-table>

      </div>
      <div class="right-content" v-if="configIdsDetail.length">
        <cropper ref="cropper" class="canvas" :loading="loadingCropper" style="margin-top: 15px">
        </cropper>
        <div class="pagination">
          <el-pagination
              background
              layout="prev, pager, next"
              :total="configIdsDetail.length"
              v-model:current-page="nowCropper"
              :page-size="1"
              class="right"
          />
        </div>
      </div>
    </div>

    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <img src="/flag.svg" id="flagimg" style="display: none;"/>
    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <StandardPackageSelectorDialog ref="configPaperSelect"
                                   @confirm="confirmChangeConfig"></StandardPackageSelectorDialog>
  </div>
</template>

<script>
import {mapState} from "pinia";
import Cropper from '@/components/cropper/forPreview.vue';
import {drawRect, img2base64} from "@/utils/imgUtil";
import StandardPackageSelectorDialog from './configPaperSelect.vue'

export default {
  components: {
    Cropper,
    StandardPackageSelectorDialog
  },
  data() {
    return {
      id: this.$route.params.id,
      columns: [
        {label: "名称", prop: "name"},
        // { label: "区域", prop: "areas", width: 100 },
        {label: "分数", prop: "areas", width: 100},
        // { label: "创建时间", prop: "createTime", width: 200  },
        {label: "操作", prop: "operations", width: 150},
      ],
      timer: null,
      file: null,
      cropperDetail: {},
      nowCropper: 1,
      paginationTaskId: null,
      autoCorrect: false,
      configIds: [],
      configIdsDetail: [],
      loadingCropper: false,
      configPackages: null,
      allPaperTotalScore: 0,
      imageList: {}
    }
  },
  created() {
    this.init()
  },
  watch: {
    "nowCropper"(val) {
      this.convertImgToBase64();
    },
    '$route.params.id'(newFileId, oldFileId) {
      this.id = newFileId;
      if (newFileId !== oldFileId) {
        this.init();
      }
    },
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.timer = null;
  },
  methods: {
    confirmChangeConfig(id, configIndex) {
      if (configIndex === -1) {
        this.configIds.push(id);
      } else {
        this.configIds[configIndex] = id;
      }
      const data = {
        id: this.id,
        name: this.configPackages.name,
        config: JSON.stringify(this.configIds)
      }
      this.$axios.post(`/api/docCorrectConfigPackage/update`, data).then(res => {
        this.$message.success("修改成功");
        this.init();
      });
    },
    init() {
      this.$axios.get("/api/docCorrectConfigPackage/getDeep?id=" + this.id).then(async res => {
        this.configPackages = res.data.configPackage;
        this.configIds = JSON.parse(res.data.configPackage.config);
        this.allPaperTotalScore = 0;

        this.configIdsDetail = res.data.configs;
        for (let i = 0; i < this.configIdsDetail.length; i++) {
          this.allPaperTotalScore += this.calculateTotalScore(JSON.parse(this.configIdsDetail[i].areas))
          await this.convertImgToBase64(i === 0, this.configIdsDetail[i].img);
        }
      })
    },
    onLook(id) {
      this.$router.push(`/docconfig?id=${id}&packageId=${this.id}`);
    },
    calculateTotalScore(areas) {
      let res = 0;
      areas.forEach(area => {
        area.questions.forEach(q => {
          res += q.score;
        })
      })
      return res;
    },
    formatDate(dateStr) {
      if (!dateStr) return ''; // 如果日期为空，返回空字符串
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    convertImgToBase64(needShow = true, imgUrl = null) {
      if (!imgUrl) {
        imgUrl = this.configIdsDetail?.[this.nowCropper - 1]?.img;
      }
      if (imgUrl) {
        if (imgUrl in this.imageList) {
          this.$refs.cropper.setImg(this.imageList[imgUrl]);
          setTimeout(() => {
            this.previewAnswer();
          }, 500)
        } else {
          if (needShow) {
            this.loadingCropper = true;
          }
          img2base64(this.$fileserver.fileurl(imgUrl)).then(res => {
            this.docImg = res;
            this.imageList[imgUrl] = res;
            if (needShow) {
              this.$refs.cropper.setImg(res);
              setTimeout(() => {
                this.previewAnswer();
              }, 500)
            }
          })
        }
      }
    },
    previewAnswer() {
      let areas = JSON.parse(this.configIdsDetail[this.nowCropper - 1].areas);
      let answers = [];
      let goucha = [];
      areas.forEach(area => {
        area.questions.forEach(q => {
          if (q.flagArea) {
            goucha.push(Object.assign({
              color: 'red',
              mark: document.getElementById("flagimg"),
            }, q.flagArea))
            answers.push({
              area: q.flagArea,
              text: q.answer,
            })
          }
        })
      })
      const img = document.getElementById("img");
      this.loadingCropper = false;
      let drawedImg = drawRect(img, goucha, answers, this.$refs.uploadCanvas)
      this.$refs.cropper.setImg(drawedImg)
    },
    /**
     * 点击"编辑名称"按钮时调用，弹出输入框修改试卷名称
     * @param {number} id - 当前行的试卷 ID
     * @param {string} currentName - 当前试卷的名称，用于在输入框中显示初始值
     */
    onEditName(id, currentName) {
      this.$prompt('请输入新的名称', '修改名称', {
        inputValue: currentName,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '新的标准卷名称',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '名称不能为空';
          }
          return true;
        },
        inputErrorMessage: '名称不能为空',
      })
        .then(({value}) => {
          const newName = value.trim();
          // 在提交更新之前，先调用后端接口检查名称是否重复
          this.$axios
            .get(`/api/docCorrectConfigPackage/checkName?name=${encodeURIComponent(newName)}`)
            .then((res) => {
              if (res.data?.exists) {
                this.$message.error('标准卷名称已存在，请使用其他名称');
                return;
              }
              // 名称不重复，继续调用更新接口
              const payload = {
                id: id,
                name: newName
              };
              this.$axios
                .post('/api/docCorrectConfigPackage/update', payload)
                .then(() => {
                  this.$message.success('修改成功！');
                  // 直接更新本地数据
                  if (this.configPackages && this.configPackages.id === id) {
                    this.configPackages.name = newName;
                  }
                  // 更新列表中的名称
                  if (this.configIdsDetail) {
                    const config = this.configIdsDetail.find(c => c.id === id);
                    if (config) {
                      config.name = newName;
                    }
                  }
                })
                .catch((err) => {
                  console.error(err);
                  this.$message.error('修改失败，请稍后重试');
                });
            })
            .catch((err) => {
              console.error(err);
              this.$message.error('检查名称时发生错误，请稍后重试');
            });
        })
        .catch(() => {
          // 用户点击"取消"或关闭弹窗
          this.$message.info('已取消修改名称');
        });
    },
    onDeleteTask(id) {
      this.$confirm("确认删除该任务吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$axios.post(`/api/docCorrectConfigPackage/deleteConfig?configID=${id}&packageId=${this.id}`).then(res => {
          this.$message.success("删除成功");
          this.init();
        });
      });
    },
    goBack() {
      const prevUrl = window.history.state?.back || '';
      if (prevUrl.includes('correctConfigPackages/upload')) {
        this.$router.go(-2);
      } else {
        this.$router.back();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table {
  table-layout: fixed;
}

.score {
  color: red;
  font-weight: bold;
  font-size: 16px;
}

.main-wrapper {
  height: 100% !important;
  display: flex;
  flex-direction: column;
  align-items: center;

  .header-bar {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    height: 48px;
    align-items: center;

    .step {
      width: 1069.83px;
      height: 42px;
      flex-shrink: 0;
      margin-left: 10px;
    }

    .icon {
      width: 28.28px;
      height: 22.89px;
    }

    .text {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-left: 10px;
    }
  }

  .main-content {
    display: flex;
    height: 100%;
    padding-bottom: 40px;
    width: 100%;
    gap: 20px;

    .left-content {
      flex: 1;
      min-width: 0;
    }

    .right-content {
      width: 50%;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 10px);
      margin-top: 10px;

      .canvas {
        height: 100%;
        width: 100%;
      }

      .pagination {
        height: 50px;
        margin-top: 5px;
        display: flex;

        .left {
          width: 200px;
          flex-shrink: 0;
        }

        .right {
          flex-grow: 1;
        }
      }
    }
  }
}

.name-link {
  cursor: pointer;
  border-radius: 4px;
  padding: 0 6px;
  transition: background 0.2s;
}
.name-link:hover {
  background: #DFF5EA;
}

/* 开始配置按钮样式 */
:deep(.el-button--primary.is-text) {
  background-color: #1677FF !important;
  border-color: #1677FF !important;
  color: #FFF !important;
  transition: all 0.3s ease;
  transform: translateY(0);
  padding: 8px 15px;
  border-radius: 6px;
  position: relative;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);

  &:hover {
    background-color: #4a90ff !important;
    border-color: #4a90ff !important;
    color: #FFF !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4);
    z-index: 20;
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15);
  }
}

/* 确保表格单元格不会裁剪按钮 */
:deep(.el-table .el-table__cell) {
  overflow: visible !important;
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

:deep(.el-table td.el-table__cell) {
  overflow: visible !important;
  position: relative;
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

/* 特别为操作列增加更多空间 */
:deep(.el-table .el-table__cell:last-child) {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

/* 确保表格行有足够高度 */
:deep(.el-table tr) {
  height: auto !important;
  min-height: 60px !important;
}
</style>