<template>
  <el-drawer
      v-model="isShow"
      size="1000px"
      direction="rtl"
      :before-close="onClose"
      custom-class="remote-folder-drawer"
  >
    <template #header>
      <div class="header-bar">
        <el-image src="/icon/16.png" class="icon"></el-image>
        <el-text class="title">远程文件夹</el-text>
      </div>

    </template>
    <div style="display: flex;align-items: center">
      <div style="flex-shrink: 0">请选择用户</div>
      <el-select
          v-model="selectUserId"
          placeholder="请输入用户名"
          filterable
          no-match-text="无匹配项"
          @change="loadFiles(selectUserId)"
          @focus="$emit('openInput')"
          @blur="$emit('closeInput')"
          style="width: 300px; margin-left: 10px;"
      >
        <el-option
            v-for="item in users"
            :key="item.id"
            :label="item.username"
            :value="item.id"
            @click.native="loadFiles(selectUserId)"
        />
      </el-select>

      <el-switch style="margin-left: 10px" active-text="只看无备注"  v-model="haveNoRemark" @change="changeHaveNoRemark"></el-switch>
    </div>
    <div style="display: flex;justify-content: space-between;align-items: center;margin: 10px 0">
      <el-breadcrumb class="breadcrumb" separator-icon="ArrowRight">
        <el-breadcrumb-item v-for="(path, index) in paths" :key="index">
          <el-button @click="onPathSelect(index)" text link>{{ index === 0 ? '首页' : path }}</el-button>
        </el-breadcrumb-item>
      </el-breadcrumb>
      <div style="display: flex;">
        <el-upload
            class="upload-demo"
            :action="$fileserver.uploadUrl"
            :with-credentials="true"
            :show-file-list="false"
            :on-success="uploadDoc"
        >
          <el-button icon="Upload" size="small" type="text" color="#3981ff" style="margin-left: 10px;color:#3981ff">上传</el-button>
        </el-upload>
        <el-button icon="FolderAdd" size="small" type="text" @click="openDialog" style="margin-left: 5px;color:#3981ff" color="#3981ff">新建文件夹</el-button>
      </div>

    </div>
    <el-table
        :data="files"
        v-loading="loading"
        height="450px"
        :highlight-current-row="true"
        @row-click="onFileSelect"
        :row-style="{ cursor: 'pointer' }"
        empty-text="无数据"
        :border="false"
    >
      <el-table-column prop="name" label="名称"  show-overflow-tooltip>
        <template #default="{ row }">
          <el-space>
            <template v-if="row.type === 1">
              <el-image src="/icon/folder.svg" style="width: 28px"></el-image>
            </template>
            <template v-else>
              <el-image src="/icon/pdf.svg" style="width: 28px"></el-image>
            </template>
            <el-text>{{ row.name }}</el-text>
          </el-space>
        </template>
      </el-table-column>
      <el-table-column prop="size" label="大小" width="70px">
        <template #default="{ row }">
          {{ row.type === 1 ? '' : formatFileSize(row.size) }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="200px">
        <template #default="{ row }">
          <el-tooltip v-if="row?.fileId || row?.configPackageId" placement="top" content="跳转到该文件配置的试卷/标准卷">
            <el-text class="clickable-underline"  @click="jump(row)">{{ row?.remark ?? '-' }}</el-text>
          </el-tooltip>
          <el-text v-else>{{ row?.remark ?? '-' }}</el-text>

        </template>
      </el-table-column>
      <el-table-column prop="modified" label="修改时间" width="170" >
        <template #default="{ row }">
          {{ dayjs(row.modified).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column prop="operation" label="操作" align="center" width="120" >
        <template #default="{ row }">
          <div  v-if="row.type !== 1" style="display: flex;align-items: center">
            <el-button icon="Download" size="small" @click.stop="downloadFile(row)">下载</el-button>
            <el-button style="margin-left: -10px;color: #3981FF !important;" size="small"  @click.stop="remark(row)">备注</el-button>
          </div>
          <el-text v-else>-</el-text>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex;flex-direction: row;margin-top: 10px;justify-content: space-between">
      <div class="hint-text">文件夹名称 格式：日期-学科-班级 例如：250516-数学-602</div>
      <div>
        <!-- <el-button type="primary" @click="selectFolder">保存/选择 当前文件夹</el-button> -->
        <el-button
          class="save-button"
          @click="openDialogInSaveFile"
          style="margin-left: 10px; background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
          @mouseenter="handleSaveMouseEnter"
          @mouseleave="handleSaveMouseLeave">
          保存
        </el-button>
      </div>
    </div>
    <el-dialog v-model="isCreateFolderDialogShow" title="输入文件夹名称" width="500px" @close="closeDialog">
      <div style="position: relative;">
        <div class="hint-text" style="position: absolute;top: -5px;left: 20px;">格式：日期-学科-班级 例如：250516-数学-602</div>
        <el-input v-model="folderName" placeholder="输入文件夹名称，不要重复" style="padding: 20px"></el-input>
      </div>

      <div style="text-align: right;padding: 10px 20px">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          class="confirm-button"
          @click="createFolder"
          style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
          @mouseenter="handleConfirmMouseEnter"
          @mouseleave="handleConfirmMouseLeave">
          确定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog v-model="isCreateAndSaveDialogShow" title="输入文件夹名称" width="500px" @close="closeDialogInSaveFile">

      <div style="padding: 0 20px;">
        <div class="hint-text">格式：日期-学科-班级 例如：250516-数学-602</div>
        <div class="hint-text" style="margin-top: 5px;">文件夹名为空则不会新建文件夹直接保存文件</div>
        <div class="hint-text" style="margin-top: 5px;">若文件夹重名则自动保存为"原文件名v2"</div>
        <div style="margin-top: 5px;font-weight: bolder" v-if="extraTip">备注： {{extraTip}}</div>
        <el-input v-model="folderName" placeholder="输入文件夹名称，不要重复" style="margin-top: 10px;"></el-input>
      </div>

      <div style="text-align: right;padding: 10px 20px">
        <el-button @click="closeDialogInSaveFile">取消</el-button>
        <el-button
          class="confirm-button-2"
          @click="createFolderThenSaveFile"
          style="background-color: #1677FF !important; color: #FFF !important; border: 1px solid #1677FF !important; font-weight: 700 !important; transition: all 0.3s ease !important;"
          @mouseenter="handleConfirm2MouseEnter"
          @mouseleave="handleConfirm2MouseLeave">
          确定
        </el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
import {Folder, Document} from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import {useUserStore} from "@/store";

const store = useUserStore();
export default {
  components: {
    Folder,
    Document
  },
  props: {
    type: {
      type: String,
      default: 'download'
    },
    paperName: {
      type: String,
      default: ''
    },
    extraTip: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dayjs,
      isShow: false,
      loading: false, // 加载状态
      paths: [''], // 路径列表，用于面包屑导航
      files: [],
      isCreateFolderDialogShow: false, // 创建文件夹对话框
      isCreateAndSaveDialogShow: false, // 创建并保存文件夹对话框
      folderName: '',
      users: [],
      selectUserId: null,
      defaultPrintUser: null,
      docList: [],
      uploadFileName: '',
      haveNoRemark: false,
      backFiles: null
    }
  },
  watch: {
    selectUserId(val) {
      if (val) {
        if (this.type === 'download') {
          store.setDownloadSelectPrintUser(this.users.find(user => user.id === val));
        } else if (this.type === 'upload') {
          store.setUploadSelectPrintUser(this.users.find(user => user.id === val));
        }
      }
    }
  },
  methods: {
    changeHaveNoRemark() {
      if (this.haveNoRemark) {
        if (!this.backFiles) {
          this.backFiles = JSON.parse(JSON.stringify(this.files));
        }
        this.files = this.backFiles.filter(it => !it?.remark)
      } else  if (this.backFiles){
        this.files = JSON.parse(JSON.stringify(this.backFiles));
      }
    },
    jump(row) {
      if (row?.fileId) {
        this.$router.push(`/markPapers/stepMarkPapers/${row.fileId}`)
      } else if(row?.configPackageId) {
        this.$router.push(`/correctConfigPackages/stepConfigPapers/${row.configPackageId}`)
      }
    },
    remark(row) {
      this.$prompt('请输入新的备注', '修改备注', {
        inputValue: row?.remark ?? '',
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(({value}) => {
        const param = {
          id: row?.id,
          remark: value,
          filename: row.name,
          modified: row.modified,
          path: this.paths.join('/'),
          size: row.size,
          type: 'remark'
        }
        this.$axios.post(`/api/ftpFilesRemark/${'remark' in row ? 'update' : 'add'}`, param).then(res => {
          // 更新
          this.$message.success("修改成功！")
          this.loadFiles();
        })

      }).catch((e) => {
        // 用户点击取消时的处理逻辑
        this.$message.info("已取消修改");
      });

    },
    uploadDoc(response, file) {
      let loadingMessage = this.$message({
        message: "正在上传中...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      const path = this.paths.join('/');
      this.docList = [];
      this.$axios.post('/api/remote/file/saveStaticFile', {
        save: true,
        path: path,
        saveFileName : file.name,
        filename: response.data.url
      }).then((res)=>{
        this.$message.success('上传成功');
      }).finally(() => {
        loadingMessage.close();
        setTimeout(()=>{this.loadFiles();}, 500);
      })
    },
    closeDialog() {
      this.isCreateFolderDialogShow = false;
      this.$emit('closeInput');
    },
    closeDialogInSaveFile() {
      this.isCreateAndSaveDialogShow = false;
      this.$emit('closeInput');
    },
    openDialog() {
      this.isCreateFolderDialogShow = true;
      // 如果有试卷名，设置为默认文件夹名
      if (this.paperName) {
        this.folderName = this.getFolderName(this.paperName);
      }
      this.$emit('openInput')
    },
    openDialogInSaveFile() {
      this.isCreateAndSaveDialogShow = true;
      // 如果有试卷名，设置为默认文件夹名
      if (this.paperName) {
        this.folderName = this.getFolderName(this.paperName);
      }
      this.$emit('openInput')
    },
    getFolderName(name) {
      if (!name) return '无';
      let nameArr = name.split('_');
      if (nameArr.length === 4) {
        return `${nameArr[0]}_${nameArr[2]}_${nameArr[3]}`;
      } else {
        return name;
      }
    },
    // 私有方法：创建文件夹的通用逻辑（自动重名加后缀v2/v3...）
    async _createFolderInternal() {
      const path = this.paths.join('/');
      let baseName = this.folderName;
      let newName = baseName;
      let suffix = 2;
      // 检查是否存在同名文件夹，自动加后缀v2/v3...
      while (this.files.some(file => file.type === 1 && file.name === newName)) {
        newName = baseName + 'v' + suffix;
        suffix++;
      }
      // 如果文件夹名被修改了，显示提示弹窗
      if (newName !== baseName) {
        this.$message({
          message: `检测到同名文件夹，已保存为${newName}`,
          type: 'success',
          duration: 3000
        });
      }
      this.folderName = newName; // 更新为最终不重名的文件夹名
      try {
        await this.$axios.get(`/api/remote/file/createFolder?path=${path}&folderName=${this.folderName}`);
        this.$message.success('创建成功');
        // 添加新文件夹到路径中
        this.paths.push(this.folderName);
        // 加载新文件夹的内容
        this.loadFiles();
        return true;
      } catch (e) {
        return false;
      }
    },
    createFolder() {
      this._createFolderInternal().then(() => {
        this.closeDialog();
      });
    },
    createFolderThenSaveFile() {
      // 添加判断，如果文件夹名称为空，则直接在当前路径保存
      if (!this.folderName || !this.folderName.trim()) {
        // 自动触发保存操作
        this.selectFolder();
        this.closeDialog();
        return; // 退出方法
      }
      // 原有逻辑：创建文件夹然后保存文件
      this._createFolderInternal().then(success => {
        if (success) {
          // 自动触发保存操作
          this.selectFolder();
        }
        this.closeDialog();
      });
    },
    show(specifyPath = '') {
      this.isShow = true;
      if (this.type === 'download') {
        let temp = store.getDownloadSelectPrintUser;
        this.defaultPrintUser = temp === null ? null : JSON.parse(JSON.stringify(temp));
      } else if (this.type === 'upload') {
        let temp = store.getUploadSelectPrintUser;
        this.defaultPrintUser = temp === null ? null : JSON.parse(JSON.stringify(temp));
      }
      if(this.type !== 'uploadByPath') {
        this.$axios.get('/api/user/allPrinterUser').then(res => {
          this.users = res.data;
          if (this.defaultPrintUser) {
            // 进行一下选择\
            const defaultPrintUser = this.defaultPrintUser;
            const selectedUser = this.users.find(user => user.id === defaultPrintUser.id);
            if (selectedUser) {
              this.selectUserId = selectedUser.id;
              this.loadFiles(this.selectUserId);
            }
            this.defaultPrintUser = null;
          }
        })
      } else {
        this.paths = specifyPath ? specifyPath.split('/') : [''];
      }

      this.loadFiles();
    },
    onClose() {
      this.paths = ['']
      this.files = []
      this.isShow = false;
    },
    // 加载文件
    loadFiles(userId = null) {
      let path = this.paths.join('/');
      if (userId) {
        path = this.users.filter(user => user.id === userId)[0].alistPath;
        this.paths = path.split('/');
      }
      this.loading = true;
      this.$axios.get(`/api/remote/file/allList?path=${path}`).then(res => {
        this.files = res.data;

        // 排序逻辑：首先按 type 排序，如果 type 相同，再按 modifiedDate 排序，最后按 name 排序
        this.files.sort((a, b) => {
          // 按 type 排序
          if (a.type !== b.type) {
            return b.type - a.type;  // 按类型从大到小排序
          }

          // 如果 type 相同，按 modifiedDate 排序（假设 modifiedDate 是日期类型）
          if (a?.modified !== b?.modified) {
            return new Date(b?.modified) - new Date(a?.modified);  // 按日期从近到远排序
          }

          // 如果 type 和 modifiedDate 都相同，按文件名排序
          return b.name.localeCompare(a.name);  // 字典序排序
        });
      }).finally(() => {
        this.loading = false;
      });

    },
    onPathSelect(index) {
      // 处理路径选择事件
      this.paths = this.paths.slice(0, index + 1);
      this.loadFiles();
    },
    formatFileSize(sizeInBytes) {
      const sizeInKB = sizeInBytes / 1024;
      if (sizeInKB < 1024) {
        return `${sizeInKB.toFixed(2)} K`;
      } else {
        const sizeInMB = sizeInKB / 1024;
        return `${sizeInMB.toFixed(2)} M`;
      }
    },
    onFileSelect(row) {
      if (row.type === 1) {
        // 如果是文件夹
        this.paths.push(row.name);
        this.loadFiles();
      } else {
        // 如果文件后缀名是 pdf
        if (row.name.endsWith('.pdf')) {
          // 弹窗确认选择该文件
          this.$confirm(`确定选择文件 ${row.name} 吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // this.loadFile(row)
            this.saveFile(row)
          })
        }
      }
    },
    downloadFile(file) {
      console.log('file download', file)
      const fileUrl = this.$fileserver.remoteFile(file, this.paths.join('/'))
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 服务器下载文件返回文件地址
    selectFolder() {
      const path = this.paths.join('/');
      this.$emit('folder-selected', path);
      this.onClose();
    },
    saveFile(file) {
      this.loading = true
      const fileUrl = this.$fileserver.remoteFile(file, this.paths.join('/'))
      // 服务器下载文件并转为 UploadFile
      this.$axios.post(`${this.$fileserver.saveRemoteFile}?url=${fileUrl}`).then(res => {
        // 创建 UploadFile 实例
        const uploadFile = {
          name: file.name,
          percentage: 100,
          uid: Date.now() + '-' + Math.random().toString(36).substring(2), // 生成唯一UID
          status: 'success',
          url: res.data.url, // 服务器返回的文件地址
        }
        // 触发选择文件的事件
        this.$emit('file-selected', uploadFile, {...file, path: this.paths.join('/')})
        this.onClose()
      }).finally(() => {
        this.loading = false
      })
    },
    // @Deprecated 修改为通过服务器下载文件并转为 UploadFile
    loadFile(file) {
      this.loading = true
      const fileUrl = this.$fileserver.remoteFile(file, this.paths.join('/'))
      // 下载文件并转为 UploadFile
      fetch(fileUrl).then(res => res.blob()).then(blob => {
        const uid = Date.now() + '-' + Math.random().toString(36).substring(2); // 生成唯一UID
        const fileObj = new File([blob], file.name, {type: blob.type}); // 创建 File 对象
        fileObj.uid = uid;
        const uploadFile = {  // 创建 UploadFile 实例
          name: file.name,
          percentage: 0,
          status: 'ready',
          uid: uid,
          size: blob.size,
          type: blob.type,
          raw: fileObj
        }
        // 触发选择文件的事件
        this.$emit('file-selected', uploadFile);
        this.onClose();
      }).catch(err => {
        console.error(err);
      }).finally(() => {
        this.loading = false
      })
    },
    handleSaveMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleSaveMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleConfirmMouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleConfirmMouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    },
    handleConfirm2MouseEnter(event) {
      const button = event.target;
      button.style.backgroundColor = '#4a90ff';
      button.style.borderColor = '#4a90ff';
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 4px 12px rgba(22, 119, 255, 0.3)';
    },
    handleConfirm2MouseLeave(event) {
      const button = event.target;
      button.style.backgroundColor = '#1677FF';
      button.style.borderColor = '#1677FF';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.el-icon) {
  color: #3981ff !important;
}
.header-bar {
  display: flex;
  align-content: center;

  .icon {
    width: 26px;
    height: 26px;
  }
  .title {
    font-weight: bolder;
    font-size: 18px;
    color: #333333;
    letter-spacing: 0;
    margin-left: 5px;
  }
}
.breadcrumb {
  .path {
    font-weight: 500;
    font-size: 15px;
    color: #999999;
  }
}

.hint-text, .model-hint {
  font-size: 13px;
  color: #999999;
  font-style: italic;
  white-space: nowrap;
}

::v-deep .el-table__header-cell {
  border-bottom: 1px solid #dcdcdc !important; /* 您希望的边框颜色 */
}

::v-deep .el-table__cell:not(th) {
  border-bottom: none !important;
  box-shadow: none !important;
}

.clickable-underline {
  color: #007BFF;
  text-decoration: underline;
  text-decoration-color: #007BFF;
  cursor: pointer;
}

.clickable-underline:hover {
  color: #0056b3;
  text-decoration-color: #0056b3;
}

</style>
