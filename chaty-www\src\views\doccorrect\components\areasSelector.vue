<template>
  <el-dialog
      title="选择所画区域"
      v-model="isVisible"
      width="600px"
      @close="handleClose"
      class="areas-selector-dialog"
  >
    <div class="button-group">
      <el-button type="primary" size="small" @click="selectAll">全选</el-button>
      <el-button type="default" size="small" @click="deselectAll">清除选择</el-button>
    </div>
    <el-checkbox-group v-model="selectedOptions" class="checkbox-grid">
      <el-checkbox v-for="option in options" :label="option.value" :key="option.value">
        区域{{ option.label + 1 }}
      </el-checkbox>
    </el-checkbox-group>
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="confirmSelection">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "AreasSelector",
  data() {
    return {
      isVisible: false,
      selectedOptions: [],
      options: [],
    };
  },
  methods: {
    show(e) {
      // 初始化 options
      this.options = e.map((item, index) => ({label: index, value: item}));
      // 默认全选
      this.selectedOptions = this.options.map(option => option.value);
      this.isVisible = true;
    },
    closeDialog() {
      this.isVisible = false;
    },
    confirmSelection() {
      // 获取选中元素的下标
      const selectedIndexes = this.selectedOptions.map(value => {
        return this.options.findIndex(option => option.value === value);
      });
      // 通过 emit 返回选中的下标数组
      this.$emit('onSelect', selectedIndexes);
      this.closeDialog();
    },
    handleClose() {
      this.selectedOptions = [];
    },
    selectAll() {
      // 全选，将所有选项的值添加到 selectedOptions 中
      this.selectedOptions = this.options.map(option => option.value);
    },
    deselectAll() {
      // 清除选择，将 selectedOptions 清空
      this.selectedOptions = [];
    },
  },
};
</script>

<style scoped>
.button-group {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 15px;
  gap: 10px;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 每行显示4个 */
  gap: 10px;
  margin-bottom: 20px;
}

/* 确认按钮样式 - 使用最强的选择器 */
.areas-selector-dialog :deep(.el-button--primary),
.areas-selector-dialog :deep(.el-dialog__footer .el-button--primary) {
  background-color: #1677FF !important;
  border-color: #1677FF !important;
  color: #FFF !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2) !important;
}

.areas-selector-dialog :deep(.el-button--primary:hover) {
  background-color: #4a90ff !important;
  border-color: #4a90ff !important;
  color: #FFF !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(22, 119, 255, 0.4) !important;
}

.areas-selector-dialog :deep(.el-button--primary:active) {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.15) !important;
}
</style>
