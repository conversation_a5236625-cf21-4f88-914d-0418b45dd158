<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex; align-items: center; gap: 24px;">
          <div style="display: flex; align-items: center;">
            <el-image src="/icon/16.png" class="left-icon"></el-image>
            <el-text class="title">问答日志</el-text>
          </div>
          <el-form inline ref="formRef" :model="formData" class="header-form" style="margin-bottom: 0;">
            <el-form-item label="试卷">
              <el-select
                  v-model="formData.fileId"
                  placeholder="请选择试卷"
                  filterable
                  remote
                  clearable
                  :remote-method="remoteSearchFiles"
                  :loading="loadingFileOptions"
                  style="width: 300px;"
              >
                <el-option
                    v-for="item in fileOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="试卷名称">
              <el-autocomplete
                  v-model="formData.recordName"
                  placeholder="请输入试卷名称"
                  clearable
                  style="width: 300px"
                  :fetch-suggestions="loadRecordOptions"
                  @select="onSelectRecord"
              />
            </el-form-item>

            <el-form-item label="区域号">
              <el-input
                  v-model="formData.areaIdx"
                  placeholder="区域号 0开始"
                  clearable
                  style="width: 100px"
              />
            </el-form-item>
            <el-form-item label="类型">
              <el-select
                  v-model="formData.type"
                  placeholder="请选择类型"
                  clearable
                  style="width: 200px"
              >
                <el-option
                    v-for="option in typeOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="请求结果">
              <el-select
                  v-model="formData.isSuccess"
                  placeholder="请选择请求结果"
                  clearable
                  style="width: 200px"
              >
                <el-option label="成功" :value="1"/>
                <el-option label="失败" :value="0"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button class="query-button" :loading="loading" @click="loadFirstPage">
                查询
              </el-button>
              <el-button v-if="showBackToPrev" linked @click="restorePrevFilters">
                返回
              </el-button>
              <el-button linked @click="reset">重置</el-button>
              <!--
              <el-button :loading="calculateLoading" class="white-button" @click="calculateAvgTime">计算平均时间</el-button>
              -->
            </el-form-item>
          </el-form>
        </div>
      </template>
    </el-page-header>

    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="height: 100%"
          empty-text="无数据"
          :border="false"
          :row-style="getRowStyle"
      >
        <el-table-column
            v-for="column in columns"
            :key="column.prop"
            v-bind="column"
            align="center"
            :fixed="column.prop === 'operations' ? 'right' : ''"
            :formatter="cellFormatter"
        >
          <!-- 区域号：为空 -> '-'；为 NaN -> '无'；否则 +1 -->
          <template v-if="column.prop === 'areaIdx'" #default="scope">
            {{ fmtAreaIdx(scope.row.areaIdx) }}
          </template>

          <!-- 请求详情：为空 -> '-'；NaN -> '无'；否则截断展示 -->
          <template v-if="column.prop === 'requestDetail'" #default="scope">
            <div class="simple-link" @click="getRequestDetail(scope.row.id)">
              {{ fmtTruncate(scope.row.requestDetail, 30) }}
            </div>
          </template>

          <!-- 响应详情：为空 -> '-'；NaN -> '无'；否则截断；没有内容时回退显示 error 也按规则处理 -->
          <template v-if="column.prop === 'responseDetail'" #default="scope">
            <div v-if="scope.row?.responseDetail" class="simple-link" @click="copy(scope.row.responseDetail)">
              {{ fmtTruncate(scope.row.responseDetail, 30) }}
            </div>
            <div v-else>{{ fmtCell(scope.row?.error) }}</div>
          </template>

          <!-- 持续时间：为空 -> '-'；NaN -> '无'；否则秒 -->
          <template v-if="column.prop === 'timeConsumption'" #default="scope">
            {{ fmtTime(scope.row.timeConsumption) }}
          </template>

          <!-- 创建时间 -->
          <template v-else-if="column.prop === 'createTime'" v-slot="scope">
            {{ isEmpty(scope.row?.createTime) ? '-' : $getFeiShuTimeFormat(scope.row?.createTime) }}
          </template>

          <template v-if="column.prop === 'isSuccess'" #default="scope">
            <el-tag :type="scope.row.isSuccess === 1 ? 'success' : 'danger'">
              {{ scope.row.isSuccess === 1 ? '成功' : '失败' }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template v-if="column.prop === 'operations'" #default="scope">
            <!-- 已有：从第一次 -> 快速查看第二次 -->
            <el-link
                v-if="scope.row.type === '普通区域-第一次请求'"
                type="primary"
                style="margin-right: 8px;"
                @click="handleSecondRequest(scope.row)"
            >
              第二次请求
            </el-link>

            <!-- 新增：从第二次 -> 快速查看第一次 -->
            <el-link
                v-if="scope.row.type === '普通区域-第二次请求'"
                type="primary"
                style="margin-right: 8px;"
                @click="handleFirstRequest(scope.row)"
            >
              第一次请求
            </el-link>

            <el-link type="primary" @click="onDelete(scope.row.id)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer-bar">
      <el-button class="next-page-button" :disabled="!hasMore" @click="loadNextPage">
        下一页
      </el-button>
    </div>

    <el-dialog
        v-model="jsonDialogVisible"
        title="查看 JSON 数据"
        width="800px"
        style="z-index: 99999;"
    >
      <json-viewer :value="currentJson"/>
      <div v-if="base64Image" style="margin-top: 20px;">
        <div style="font-weight: bold; margin-bottom: 10px;">图片预览：</div>
        <img :src="base64Image" style="max-width: 100%; max-height: 300px; border: 1px solid #eee;"/>
      </div>

      <el-button
          type="primary"
          v-if="!showResponse && '$response' in currentJson"
          @click="showResponseObj(currentJson)"
      >
        复制查看 $response
      </el-button>
      <div v-if="showResponse && responseObj">
        <div>$response 的内容:</div>
        <json-viewer :value="responseObj"/>
      </div>
      <template #footer>
        <el-button @click="jsonDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <el-dialog
        v-model="statsDialogVisible"
        title="平均耗时统计"
        width="400px"
    >
      <div style="padding: 10px; font-size: 16px;">
        平均耗时：{{ (statsResult.averageTimeConsumption).toFixed(2) }} s
      </div>
      <div style="padding: 0 10px; font-size: 16px;">
        {{ (statsResult.totalTimeConsumption).toFixed(2) }}s / {{ statsResult?.totalCount }}个
      </div>
      <div style="padding: 0 10px; font-size: 16px;">
        无请求时间的数据： {{ statsResult?.nullCount }}个
      </div>
      <template #footer>
        <el-button type="primary" @click="statsDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {JsonViewer} from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';

export default {
  name: 'RequestLog',
  components: {JsonViewer},
  data() {
    return {
      formData: {
        fileId: null,
        recordName: '',
        type: '',
        isSuccess: null,
        recordId: '',
        areaIdx: null,
      },
      tableData: [],
      columns: [
        {label: '序号', type: 'index', width: 100},
        {label: '试卷名称', prop: 'recordName'},
        {label: '区域号', prop: 'areaIdx'},
        {label: '模型名称', prop: 'aimodel'},
        {label: '请求详情', prop: 'requestDetail'},
        {label: '响应详情', prop: 'responseDetail'},
        {label: '类型', prop: 'type'},
        {label: '请求结果', prop: 'isSuccess'},
        {label: '持续时间', prop: 'timeConsumption'},
        {label: '创建时间', prop: 'createTime'},
        {label: '操作', prop: 'operations', width: 150},
      ],
      pageSize: 10,
      lastCreateTime: null,
      hasMore: false,
      loading: false,
      jsonDialogVisible: false,
      currentJson: null,
      typeOptions: [],
      showResponse: false,
      responseObj: null,
      statsDialogVisible: false,
      statsResult: {averageTimeConsumption: 0},
      calculateLoading: false,
      tempOptions: [],
      loadingTempOption: false,
      base64Image: null,
      fileOptions: [],           // 试卷列表
      loadingFileOptions: false, // 远程加载状态
      colorPalette: ['#e0ece0','#f1f6f0','#eef5fb','#ECEFF1'],
      nameColorMap: {},
      colorPaletteIdx: 0,
      prevFilters: null,     // 缓存“跳转前”的筛选条件
      showBackToPrev: false, // 控制“返回”按钮显示
    };
  },
  methods: {
    // 新增：从“第二次请求”快速切到“第一次请求”
    handleFirstRequest(row) {
      // 缓存当前筛选条件
      this.prevFilters = JSON.parse(JSON.stringify(this.formData));
      this.showBackToPrev = true;

      // 手动清理状态（避免多一次 reset 触发请求）
      this.formData = {
        fileId: null,
        recordName: '',
        type: '',
        isSuccess: null,
        recordId: '',
        areaIdx: null,
      };
      this.tableData = [];
      this.lastCreateTime = null;
      this.hasMore = false;

      // 设置为“普通区域-第一次请求”的筛选条件
      this.formData.recordName = row.recordName || '';
      this.formData.areaIdx = this.isEmpty(row.areaIdx) ? null : row.areaIdx;
      this.formData.type = '普通区域-第一次请求';

      // 查询
      this.loadFirstPage();
    },

    // 已有：从“第一次请求”快速切到“第二次请求”
    handleSecondRequest(row) {
      this.prevFilters = JSON.parse(JSON.stringify(this.formData));
      this.showBackToPrev = true;

      this.formData = {
        fileId: null,
        recordName: '',
        type: '',
        isSuccess: null,
        recordId: '',
        areaIdx: null,
      };
      this.tableData = [];
      this.lastCreateTime = null;
      this.hasMore = false;

      this.formData.recordName = row.recordName || '';
      this.formData.areaIdx = this.isEmpty(row.areaIdx) ? null : row.areaIdx;
      this.formData.type = '普通区域-第二次请求';

      this.loadFirstPage();
    },

    restorePrevFilters() {
      if (!this.prevFilters) return;
      this.formData = JSON.parse(JSON.stringify(this.prevFilters));
      this.prevFilters = null;
      this.showBackToPrev = false;
      this.loadFirstPage();
    },

    // === 统一判空/NaN/格式化 ===
    isEmpty(val) {
      return val === null || val === undefined || (typeof val === 'string' && val.trim() === '');
    },
    isNaNish(val) {
      if (typeof val === 'number') return Number.isNaN(val);
      if (typeof val === 'string') return val.trim().toLowerCase() === 'nan';
      return false;
    },
    fmtCell(val) {
      if (this.isEmpty(val)) return '-';
      if (this.isNaNish(val)) return '无';
      return val;
    },
    cellFormatter(row, column, cellValue) {
      return this.fmtCell(cellValue);
    },
    fmtTruncate(val, len = 30) {
      const v = this.fmtCell(val);
      if (v === '-' || v === '无') return v;
      const s = String(v);
      return s.length > len ? s.slice(0, len) + '...' : s;
    },
    fmtAreaIdx(val) {
      if (this.isEmpty(val)) return '-';
      if (this.isNaNish(val)) return '无';
      const n = Number(val);
      if (Number.isNaN(n)) return '无';
      return n + 1;
    },
    fmtTime(ms) {
      if (this.isEmpty(ms)) return '-';
      if (this.isNaNish(ms)) return '无';
      const n = Number(ms);
      if (Number.isNaN(n)) return '无';
      return (n / 1000).toFixed(1) + 's';
    },

    loadRecordOptions(queryString, cb) {
      if (!queryString) { cb([]); return; }
      this.$axios
          .post('/api/docCorrectRecord/page', { page: { pageNumber: 1, pageSize: 10 }, name: queryString })
          .then(res => {
            const recs = Array.isArray(res.data.records) ? res.data.records : [];
            const suggestions = recs.map(r => ({ value: r.docname, id: r.id }));
            cb(suggestions);
          })
          .catch(() => cb([]));
    },
    onSelectRecord(item) { this.formData.fileId = item.id; },

    remoteSearchFiles(query) {
      if (!query) { this.fileOptions = []; return; }
      this.loadingFileOptions = true;
      this.$axios.post('/api/docCorrectFile/page', { name: query, page: { pageNumber: 1, pageSize: 20 } })
          .then(res => {
            const recs = res.data.records || [];
            this.fileOptions = recs.map(r => ({ id: r.id, name: r.name }));
          })
          .catch(() => { this.fileOptions = []; })
          .finally(() => { this.loadingFileOptions = false; });
    },
    loadCorrectFile(name) {
      return this.$axios.post("/api/docCorrectFile/page", {
        name, page: { pageNumber: 1, pageSize: -1 }
      }).then(res => res.data.records);
    },
    loadTempOptions(name, cb) {
      if (!name) { cb([]); return; }
      this.loadingTempOption = true;
      this.$axios.post("/api/docCorrectFile/page", { name, page: { pageNumber: 1, pageSize: -1 } })
          .then(res => {
            const recs = res.data.records || [];
            const list = recs.map(obj => ({ value: obj.name, id: obj.id }));
            this.tempOptions = recs;
            cb(list);
          })
          .finally(() => { this.loadingTempOption = false; });
    },
    onSelectFile(item) { this.formData.fileId = item.id; },

    queryRecordName(queryString, cb) {
      if (!queryString) { cb([]); return; }
      this.$axios.get('/api/gptAskLog/distinct', { params: { search: queryString } })
          .then(res => {
            const list = Array.isArray(res.data) ? res.data.map(name => ({ value: name })) : [];
            cb(list);
          })
          .catch(() => cb([]));
    },

    calculateAvgTime() {
      if (!this.formData.recordName) { this.$message.warning('请输入记录名称'); return; }
      this.calculateLoading = true;
      this.$axios.get('/api/gptAskLog/stats', { params: { recordName: this.formData.recordName } })
          .then(res => {
            this.statsResult = res.data || { averageTimeConsumption: 0 };
            this.statsDialogVisible = true;
          })
          .catch(() => { this.$message.error('获取统计数据失败'); })
          .finally(() => { this.calculateLoading = false; });
    },

    showResponseObj(e) {
      this.showResponse = true;
      this.responseObj = JSON.parse(e['$response']);
      this.base64Image = null;
      this.$nextTick(() => { this.findBase64Image(this.responseObj); });
      navigator.clipboard.writeText(e['$response']).then(() => { this.$message.success('复制成功'); });
    },

    reset() {
      this.formData = {
        recordName: '',
        type: '',
        isSuccess: null,
        recordId: '',
        areaIdx: null,
      };
      this.tableData = [];
      this.lastCreateTime = null;
      this.hasMore = false;
      this.prevFilters = null;
      this.showBackToPrev = false;
      this.loadFirstPage();
    },

    getRequestDetail(id) {
      this.loading = true;
      this.$axios.post('/api/gptAskLog/page', { page: { pageSize: 9999 }, id, needBase64: true })
          .then(res => {
            const data = res.data.records[0];
            this.copy(data.requestDetail);
          })
          .finally(() => { this.loading = false; });
    },
    copy(jsonStr) {
      navigator.clipboard.writeText(jsonStr).then(() => { this.$message.success('复制成功'); });
      this.showJsonDialog(JSON.parse(jsonStr));
    },
    showJsonDialog(data) {
      this.currentJson = data;
      this.showResponse = false;
      this.base64Image = null;
      this.jsonDialogVisible = true;
      this.$nextTick(() => { this.findBase64Image(data); });
    },
    findBase64Image(obj) {
      if (!obj || typeof obj !== 'object') return;
      Object.keys(obj).forEach(key => {
        const value = obj[key];
        if (typeof value === 'string' && value.startsWith('data:image/')) {
          this.base64Image = value; return;
        }
        if (typeof value === 'object') { this.findBase64Image(value); }
      });
    },

    goBack() { this.$router.back(); },

    loadFirstPage() {
      this.loading = true;
      this.$axios.post('/api/gptAskLog/page', {
        page: { pageSize: this.pageSize },
        fileId: this.formData.fileId,
        recordName: this.formData.recordName,
        type: this.formData.type,
        isSuccess: this.formData.isSuccess,
        areaIdx: this.formData.areaIdx,
        recordId: this.formData.recordId,
        lastCreateTime: null,
        needBase64: false,
      })
          .then(res => {
            const recs = res.data.records;
            this.calculateNameColorMap(recs);
            this.tableData = recs;
            this.lastCreateTime = recs.length ? recs[recs.length - 1].createTime : null;
            this.hasMore = recs.length === this.pageSize;
          })
          .finally(() => { this.loading = false; });
    },

    loadNextPage() {
      if (!this.hasMore) return;
      this.loading = true;
      this.$axios.post('/api/gptAskLog/page', {
        page: { pageSize: this.pageSize },
        fileId: this.formData.fileId,
        recordName: this.formData.recordName,
        type: this.formData.type,
        isSuccess: this.formData.isSuccess,
        areaIdx: this.formData.areaIdx,
        recordId: this.formData.recordId,
        lastCreateTime: this.lastCreateTime,
        needBase64: false,
      })
          .then(res => {
            const recs = res.data.records;
            this.calculateNameColorMap(this.tableData.concat(recs));
            this.tableData = this.tableData.concat(recs);
            this.lastCreateTime = recs.length ? recs[recs.length - 1].createTime : this.lastCreateTime;
            this.hasMore = recs.length === this.pageSize;
          })
          .finally(() => { this.loading = false; });
    },

    onDelete(id) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
          .then(() => this.$axios.delete(`/api/gptAskLog/${id}`))
          .then(() => { this.$message.success('删除成功'); this.reset(); })
          .catch(() => {});
    },

    loadTypeOptions() {
      this.$axios.get('/api/gptAskLog/allType')
          .then(res => { this.typeOptions = Array.isArray(res.data) ? res.data : []; })
          .catch(() => { this.$message.error('加载类型列表失败'); });
    },

    calculateNameColorMap(data) {
      const freq = {};
      data.forEach(row => {
        const groupKey = row.recordId ? String(row.recordId).slice(0, 6) : '';
        if (groupKey) freq[groupKey] = (freq[groupKey] || 0) + 1;
      });
      const map = {};
      let idx = 0;
      const colorPalette = this.colorPalette;
      data.forEach(row => {
        const groupKey = row.recordId ? String(row.recordId).slice(0, 6) : '';
        if (groupKey && freq[groupKey] > 1 && !map[groupKey]) {
          map[groupKey] = colorPalette[idx % colorPalette.length];
          idx++;
        }
      });
      this.nameColorMap = map;
      this.colorPaletteIdx = idx;
    },
    getRowStyle({row}) {
      const groupKey = row.recordId ? String(row.recordId).slice(0, 6) : '';
      const bg = this.nameColorMap[groupKey];
      return bg ? {background: bg} : {};
    },
  },
  created() {
    const params = this.$route.params;
    if (params) {
      this.formData.recordId = params.recordId || '';
      this.formData.areaIdx = params.areaIdx ?? null;
    }
    this.loadTypeOptions();
    this.loadFirstPage();
  },
};
</script>

<style lang="scss" scoped>
.simple-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
}
.simple-link:hover { color: #40a9ff; }

.white-button { background-color: #ffffff; border-color: #dcdfe6; color: #606266; }
.white-button:hover { background-color: #f5f7fa; border-color: #c0c4cc; color: #606266; }
.white-button:active { background-color: #e6e8eb; border-color: #c0c4cc; color: #606266; }

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form { :deep(.el-form-item) { margin-bottom: 0; } }

    .left-icon { width: 28.28px; height: 22.89px; transform: scaleX(-1); }

    .title { font-weight: bold; font-size: 18px; color: #333333; letter-spacing: 0; margin-right: 19px; }
  }

  .main-content { flex: 1; }

  .footer-bar {
    height: 80px; display: flex; align-items: center; justify-content: center;
  }

  :deep(.query-button) {
    background-color: #1677FF !important; color: #FFF !important; border: none !important; font-weight: 700 !important; transition: all 0.3s ease !important;
    &:hover { background-color: #4a90ff !important; color: #FFF !important; transform: translateY(-2px) !important; box-shadow: 0 4px 12px rgba(22,119,255,0.3) !important; }
    &:focus { background-color: #4a90ff !important; color: #FFF !important; }
    &:active { background-color: #1677FF !important; color: #FFF !important; }
    &.is-loading { background-color: #1677FF !important; color: #FFF !important; }
  }

  :deep(.next-page-button) {
    background-color: #1677FF !important; color: #FFF !important; border: none !important; font-weight: 700 !important; transition: all 0.3s ease !important;
    &:hover:not(.is-disabled) { background-color: #4a90ff !important; color: #FFF !important; transform: translateY(-2px) !important; box-shadow: 0 4px 12px rgba(22,119,255,0.3) !important; }
    &:focus:not(.is-disabled) { background-color: #4a90ff !important; color: #FFF !important; }
    &:active:not(.is-disabled) { background-color: #1677FF !important; color: #FFF !important; }
    &.is-disabled { background-color: #c0c4cc !important; color: #ffffff !important; cursor: not-allowed !important; transform: none !important; box-shadow: none !important; }
  }
}
</style>
